package com.thelastblockbender.bloodrip;

import java.util.List;
import java.util.Random;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.logging.Level;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle.DustOptions;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.BloodAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class BloodRip extends BloodAbility implements AddonAbility {

  private double range;
  private long cooldown ;
  private long chargeTime;
  private double maxDamage;
  private boolean bloodSource;
  private boolean canKill = true;
  private int bloodSourceUses;

  private LivingEntity entity;
  private long lastChargeDisplay;

  public static Random random = new Random();

  public static List<Entity> dedPlayers = new CopyOnWriteArrayList<>();

  public BloodRip(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, BloodRip.class)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());

    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    chargeTime = TLBMethods.getLong("ExtraAbilities.StrangeOne101.BloodRip.ChargeTime", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.StrangeOne101.BloodRip.Range", currentLevel);
    maxDamage = TLBMethods.getDouble("ExtraAbilities.StrangeOne101.BloodRip.MaxDamage", currentLevel);
    cooldown = TLBMethods.getLong("ExtraAbilities.StrangeOne101.BloodRip.Cooldown", currentLevel);
    canKill = ConfigManager.defaultConfig.get().getBoolean("ExtraAbilities.StrangeOne101.BloodRip.CanKill");
    bloodSourceUses = ConfigManager.defaultConfig.get().getInt("ExtraAbilities.StrangeOne101.BloodRip.BloodSource.MaxUses");
    bloodSource = ConfigManager.defaultConfig.get().getBoolean("ExtraAbilities.StrangeOne101.BloodRip.BloodSource.Enabled");

    boolean canOnlyBeUsedAtNight = getConfig().getBoolean("Abilities.Water.Bloodbending.CanOnlyBeUsedAtNight");
    //this.canBeUsedOnUndeadMobs = getConfig().getBoolean("Abilities.Water.Bloodbending.CanBeUsedOnUndeadMobs");
    //private boolean canBeUsedOnUndeadMobs;
    boolean onlyUsableDuringMoon = getConfig().getBoolean("Abilities.Water.Bloodbending.CanOnlyBeUsedDuringFullMoon");
    //this.canBloodbendOtherBloodbenders = getConfig().getBoolean("Abilities.Water.Bloodbending.CanBloodbendOtherBloodbenders");

    if (canOnlyBeUsedAtNight && !isNight(player.getWorld()) && !bPlayer.canBloodbendAtAnytime()) {
      remove();
      return;
    } else if (onlyUsableDuringMoon && !isFullMoon(player.getWorld()) && !bPlayer.canBloodbendAtAnytime()) {
      remove();
      return;
    }
    this.lastChargeDisplay = System.currentTimeMillis() - 1000;

    Entity target = GeneralMethods.getTargetedEntity(player, range);
    if (target instanceof LivingEntity) {
      this.entity = (LivingEntity) target;
      start();
    }
  }

  @Override
  public void progress() {
    if (!bPlayer.canBend(this) || entity == null || !entity.isValid() || !entity.getWorld().equals(player.getWorld()) ||
      player.getLocation().distanceSquared(entity.getLocation()) > range * range) {
      remove();
      return;
    }


    long neededChargeTime = (long) (chargeTime / (Math.max(this.entity.getAttribute(Attribute.MAX_HEALTH).getValue(), maxDamage)) * this.entity.getHealth());
    long chargeTime = System.currentTimeMillis() - this.getStartTime();
    if (chargeTime > neededChargeTime) chargeTime = neededChargeTime;

    if (this.lastChargeDisplay + 1000 < System.currentTimeMillis() || chargeTime >= neededChargeTime) {
      displayCharge(1F / neededChargeTime * chargeTime);
      for (int i = 1; i < (chargeTime >= neededChargeTime ? 2 : 8); i++) {
        ParticleEffect.SMOKE_NORMAL.display(this.entity.getLocation().add(0.5 - random.nextFloat(), 0.7 + random.nextFloat() / 2, 0.5 - random.nextFloat()), 1, 0, 0.1, 0, 0.5);
        //ParticleEffect.SMOKE.display(new Vector(0, 0.1, 0), 0.5F, this.entity.getLocation().add(0.5 - random.nextFloat(), 0.7 + random.nextFloat() / 2, 0.5 - random.nextFloat()), 257D);
      }
      this.lastChargeDisplay = System.currentTimeMillis();
    }

    if (!this.player.isSneaking()) {
      if (chargeTime >= 1000L) {
        affect(maxDamage / neededChargeTime * chargeTime);
        bPlayer.addCooldown(this);
        remove();
      } else {
        remove();
      }

    }
  }

  public double getKillDamage() {
    long neededChargeTime = (long) (chargeTime / (Math.max(this.entity.getAttribute(Attribute.MAX_HEALTH).getValue(), maxDamage)) * this.entity.getHealth());
    long chargeTime = System.currentTimeMillis() - this.getStartTime();

    return maxDamage / neededChargeTime * chargeTime;
  }

  private void displayCharge(float charge) {
    Location location = GeneralMethods.getRightSide(this.player.getLocation(), 0.55D).add(0.0D, 1.3D, 0.0D).toVector().add(this.player.getEyeLocation().getDirection().clone().multiply(0.75D)).toLocation(this.player.getWorld());
    DustOptions options = new DustOptions(Color.RED, 1);
    ParticleEffect.REDSTONE.display(location, 0, 255 * charge, 0.0, 0.0, 0.004, options);
    ParticleEffect.REDSTONE.display(location, 0, 255 * charge, 0.0, 0.0, 0.004, options);
    //ParticleEffect.RED_DUST.display(255 * charge, 0, 0, 0.004F, 0, location, 257D);
    //ParticleEffect.RED_DUST.display(255 * charge, 0, 0, 0.004F, 0, location, 257D);
  }

  public void affect(double damage) {
    if (this.entity.getHealth() <= damage && damage > 0.5D) {
      if (!canKill) {
        damage = this.entity.getHealth() - 0.5D;
      } else if (bloodSource) {
        dedPlayers.add(this.entity);

        new BukkitRunnable() {
          @Override
          public void run() {
            dedPlayers.remove(entity);
          }
        }.runTaskLater(ProjectKorra.plugin, 2L);
      }

    }
    DamageHandler.damageEntity(entity, damage, this);
    for (int i = 0; i < 16; i++) {
      DustOptions options = new DustOptions(Color.RED, 1);
      ParticleEffect.REDSTONE.display(this.entity.getLocation().add(0.5 - random.nextFloat(), -0.25D + random.nextFloat(), 0.5 - random.nextFloat()), 1, 0, 0, 0, 0D, options);
    }
    ItemStack redstoneTexture = new ItemStack(Material.REDSTONE_BLOCK);
    ParticleEffect.ITEM_CRACK.display(this.entity.getLocation(), 48, 0, 0.4, 0, 0.5, redstoneTexture);
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public String getName() {
    return "BloodRip";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public String getAuthor() {
    return "StrangeOne101 (updated by DeminBell)";
  }

  @Override
  public String getVersion() {
    return "1.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new BloodRipListener(), ProjectKorra.plugin);

    ProjectKorra.plugin.getLogger().log(Level.INFO, getName() + " v" + getVersion() + " by " + getAuthor() + " loaded!");
    ProjectKorra.plugin.getLogger().log(Level.INFO, "You're a good person for installing this ability. Take that how you will. :)");

      ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.ChargeTime", 5000);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.Range", 8);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.Cooldown", 10000);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.BloodSource.Enabled", true);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.BloodSource.MaxTime", 5000);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.CanKill", true);
    ConfigManager.defaultConfig.get().addDefault("ExtraAbilities.StrangeOne101.BloodRip.MaxDamage", 12);


    ConfigManager.defaultConfig.save();

    ConfigManager.languageConfig.get().addDefault("Abilities.Water.BloodRip.DeathMessage", "{victim} was purged by {attacker}'s {ability}");

    ConfigManager.languageConfig.save();
  }

  @Override
  public void stop() {
    for (BloodSource source : BloodSource.instances.values()) {
      source.remove();
    }
  }

  @Override
  public String getDescription() {
    return "BloodRip is a dark Bloodbending move. Hold Sneak to charge up the move while looking at a player or a mob, then release Sneak to rip the blood out of them.";
  }

  @Override
  public Location getLocation() {
    return this.entity != null ? this.entity.getLocation() : this.player.getLocation();
  }

  @Override
  public void remove() {
    super.remove();
  }

}
