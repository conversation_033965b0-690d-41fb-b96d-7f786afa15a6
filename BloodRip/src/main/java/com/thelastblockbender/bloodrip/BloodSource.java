package com.thelastblockbender.bloodrip;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Material;
import org.bukkit.Particle.DustOptions;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;

public class BloodSource {
  public static Map<Block, BloodSource> instances = new ConcurrentHashMap<>();

  private final Block block;
  private BukkitRunnable task;
  private BlockData oldData;
  private int usesLeft;
  private long expiry;
  private final long startTime;

  public BloodSource(Block block) {
    this.block = block;
    this.startTime = System.currentTimeMillis();
    this.expiry = ConfigManager.defaultConfig.get().getLong("ExtraAbilities.StrangeOne101.BloodRip.BloodSource.MaxTime");

    // If it's solid, don't bother
    if (block.getType().isSolid()) {
      return;
    }

    final BloodSource instance = this;

    if (block.getType() != Material.WATER) {
      this.oldData = block.getBlockData().clone();
      block.setType(Material.WATER);

      new BukkitRunnable() {
        public void run() {
          for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendBlockChange(instance.block.getLocation(), instance.oldData);
          }
        }
      }.runTaskLater(ProjectKorra.plugin, 1L);

    }
    instances.put(block, this);


    task = new BukkitRunnable() {

      final Random random = new Random();

      @Override
      public void run() {
        if (usesLeft <= 0 || System.currentTimeMillis() > startTime + expiry || instance.block.getType() != Material.WATER) {
          remove();
        } else {
          for (int i = 0; i < 16; i++) {
            DustOptions options = new DustOptions(Color.RED, 1);
            ParticleEffect.REDSTONE.display(instance.block.getLocation().clone().add(random.nextFloat(), random.nextFloat(), random.nextFloat()), 0, 0.0, 0.0, 0.0, options);
            //ParticleEffect.RED_DUST.display(0, 0, 0, 0.004F, 0, instance.block.getLocation().clone().add(random.nextFloat(), random.nextFloat(), random.nextFloat()), 80D);
          }
          ItemStack Red_DustTexture = new ItemStack(Material.REDSTONE_BLOCK);
          ParticleEffect.ITEM_CRACK.display(instance.block.getLocation().clone().add(0.5, 0.5, 0.5), 40, 0, 0, 0, 1, Red_DustTexture);
          //ParticleEffect.BLOCK_CRACK.display(new ParticleEffect.BlockData(Material.Red_Dust_BLOCK, (byte)0), 0F, 0F, 0F, 1, 40, instance.block.getLocation().clone().add(0.5, 0.5, 0.5), 80);
        }
      }

    };

    task.runTaskTimer(ProjectKorra.plugin, 1L, 4L);
  }

  public void remove() {
    task.cancel();
    instances.remove(block);
    this.block.setType(oldData.getMaterial());
    this.block.setBlockData(oldData);
  }


  public Block getBlock() {
    return block;
  }

  public void setExpiry(long expiry) {
    this.expiry = expiry;
  }

  public long getExpiry() {
    return expiry;
  }

  public void setUsesLeft(int usesLeft) {
    this.usesLeft = usesLeft;
  }

  public int getUsesLeft() {
    return usesLeft;
  }
}
