package me.moros.aircocoon;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class CocoonListener implements Listener {
	@EventHandler(ignoreCancelled = true)
	public void onSneak(PlayerToggleSneakEvent event) {
		if (!event.isSneaking()) return;
		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		if (bPlayer == null) return;
		if ("AirCocoon".equalsIgnoreCase(bPlayer.getBoundAbilityName()) && bPlayer.canCurrentlyBendWithWeapons()) {
			new AirCocoon(player);
		}
	}

	@EventHandler(ignoreCancelled = true)
	public void onSwing(PlayerAnimationEvent event) {
		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		if (bPlayer == null) return;
		if ("AirCocoon".equalsIgnoreCase(bPlayer.getBoundAbilityName()) && bPlayer.canCurrentlyBendWithWeapons()) {
			AirCocoon ability = CoreAbility.getAbility(event.getPlayer(), AirCocoon.class);
			if (ability != null) ability.startSoar();
		}
	}
}
