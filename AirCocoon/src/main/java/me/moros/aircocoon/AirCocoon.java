package me.moros.aircocoon;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.airbending.AirSwipe;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.earthbending.EarthBlast;
import com.projectkorra.projectkorra.earthbending.EarthSmash;
import com.projectkorra.projectkorra.earthbending.Shockwave;
import com.projectkorra.projectkorra.firebending.FireBlast;
import com.projectkorra.projectkorra.firebending.FireBlastCharged;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.SurgeWave;
import com.projectkorra.projectkorra.waterbending.Torrent;
import com.projectkorra.projectkorra.waterbending.WaterManipulation;
import com.projectkorra.projectkorra.waterbending.multiabilities.WaterArms;


import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;

public class AirCocoon extends AirAbility implements AddonAbility {
	private enum Mode {SHIELD, SOAR}

	private Mode mode = Mode.SHIELD;

	private int currPoint;
	private int animationSpeed;
	private long cooldown;
	private long soarCooldown;
	private long duration;
	private long soarDuration;
	private double collisionRadius;
	private double soarSpeed;

	private long startTime;

	public AirCocoon(Player player) {
		super(player);

		if (hasAbility(player, AirCocoon.class) || !bPlayer.canBend(this)) {
			return;
		}

		startTime = System.currentTimeMillis();
		modify();
		start();
	}

	private void modify() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

		collisionRadius = TLBMethods.getDouble("ExtraAbilities.Moros.AirCocoon.CollisionRadius", currentLevel);
		cooldown = TLBMethods.getLong("ExtraAbilities.Moros.AirCocoon.Cooldown", currentLevel);
		duration = TLBMethods.getLong("ExtraAbilities.Moros.AirCocoon.Duration", currentLevel);
		soarCooldown = TLBMethods.getLong("ExtraAbilities.Moros.AirCocoon.Soar.Cooldown", currentLevel);
		soarDuration = TLBMethods.getLong("ExtraAbilities.Moros.AirCocoon.Soar.Duration", currentLevel);
		soarSpeed = TLBMethods.getLong("ExtraAbilities.Moros.AirCocoon.Soar.Speed", currentLevel);
		animationSpeed = currentLevel >= 5 ? 3 : 2;
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return player.getLocation().add(0, 1, 0);
	}

	@Override
	public String getName() {
		return "AirCocoon";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public void progress() {
		if (!bPlayer.canBend(this) || !player.isSneaking() || System.currentTimeMillis() > startTime + duration) {
			bPlayer.addCooldown(this);
			remove();
			return;
		}

		Location center = getLocation();
		playAirbendingSound(center);
		if (mode == Mode.SOAR) {
			player.setVelocity(player.getEyeLocation().getDirection().multiply(soarSpeed));
			player.setFallDistance(0);
			ParticleEffect.CLOUD.display(center, 4, 0.5, 0.5, 0.5, 0.2);
			playAirbendingParticles(center, 2, 0.25, 0.25, 0.25);
		} else {
			for (int i = 0; i < animationSpeed; i++) {
				currPoint = (currPoint + 6) % 360;
				double angle = Math.toRadians(currPoint);
				double x = Math.cos(angle);
				double z = Math.sin(angle);
				playAirbendingParticles(player.getLocation().add(x, 0, z), 1, 0.1, 0.1, 0.1);
				playAirbendingParticles(player.getLocation().add(x, 2, z), 1, 0.1, 0.1, 0.1);
				playAirbendingParticles(player.getLocation().add(-x, 1, -z), 1, 0.1, 0.1, 0.1);
			}
		}
	}

	public void startSoar() {
		if (mode == Mode.SOAR) return;
		mode = Mode.SOAR;
		startTime = System.currentTimeMillis();
		cooldown = soarCooldown;
		duration = soarDuration;
		Location currentLoc = player.getLocation();
		if (currentLoc.getBlock().getType() == Material.ICE) {
			for (final Location l : GeneralMethods.getCircle(currentLoc, 3, 4, false, true, 0)) {
				Block block = l.getBlock();
				if (block.getType() != Material.ICE) continue;
				ParticleEffect.BLOCK_CRACK.display(l, 5, 0, 0, 0, 0, Material.PACKED_ICE.createBlockData());
				l.getWorld().playSound(l, Sound.BLOCK_GLASS_BREAK, 2f, 5f);
				if (TempBlock.isTempBlock(block)) {
					TempBlock temp = TempBlock.get(block);
					if (temp.getState().getType() == Material.WATER) {
						temp.revertBlock();
						continue;
					}
				}
				block.setType(Material.AIR);
			}
		}
	}

	@Override
	public boolean isCollidable() {
		return mode == Mode.SHIELD;
	}

	@Override
	public double getCollisionRadius() {
		return collisionRadius;
	}

	@Override
	public void handleCollision(Collision collision) {
		super.handleCollision(collision);
		boolean remove = false;
		if (collision.isRemovingFirst()) {
			ParticleEffect.EXPLOSION_NORMAL.display(collision.getLocationFirst(), 1, 1, 1, 1, 0.1);
			remove = true;
		}
		if (collision.isRemovingSecond()) {
			ParticleEffect.EXPLOSION_NORMAL.display(collision.getLocationSecond(), 1, 1, 1, 1, 0.1);
			remove = true;
		}
		if (remove) {
			bPlayer.addCooldown(this);
			remove();
		}
	}

	@Override
	public String getAuthor() {
		return "Moros";
	}

	@Override
	public String getVersion() {
		return "2.0";
	}

	@Override
	public String getDescription() {
		return "Creates a small shield-like cocoon of air that wraps around you and provides small protection blocking some attacks. You can also launch yourself in the direction you are looking at.";
	}

	@Override
	public String getInstructions() {
		return "Hold sneak to generate a cocoon shield. Left click to soar for a short duration of time.";
	}

	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new CocoonListener(), ProjectKorra.plugin);

		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.CollisionRadius", 2);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.Cooldown", 9000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.Duration", 8000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.Soar.Cooldown", 7000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.Soar.Duration", 800);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.AirCocoon.Soar.Speed", 1.5);
		ConfigManager.defaultConfig.save();

		CoreAbility fireBlast = CoreAbility.getAbility(FireBlast.class);
		CoreAbility earthBlast = CoreAbility.getAbility(EarthBlast.class);
		CoreAbility waterManip = CoreAbility.getAbility(WaterManipulation.class);
		CoreAbility airSwipe = CoreAbility.getAbility(AirSwipe.class);
		CoreAbility fireBlastCharged = CoreAbility.getAbility(FireBlastCharged.class);
		CoreAbility torrent = CoreAbility.getAbility(Torrent.class);
		CoreAbility surge = CoreAbility.getAbility(SurgeWave.class);
		CoreAbility waterArms = CoreAbility.getAbility(WaterArms.class);
		CoreAbility earthSmash = CoreAbility.getAbility(EarthSmash.class);
		CoreAbility shockwave = CoreAbility.getAbility(Shockwave.class);

		CoreAbility mainAbil = CoreAbility.getAbility(AirCocoon.class);

		for (CoreAbility smallAbil : new CoreAbility[] {airSwipe, earthBlast, waterManip, fireBlast, fireBlastCharged, torrent, surge, waterArms, earthSmash, shockwave}) {
			ProjectKorra.getCollisionManager().addCollision(new Collision(mainAbil, smallAbil, true, true));
		}
		ProjectKorra.log.info("Loaded " + getName() + " v" + getVersion() + " by " + getAuthor());
	}

	@Override
	public void stop() {
	}
}
