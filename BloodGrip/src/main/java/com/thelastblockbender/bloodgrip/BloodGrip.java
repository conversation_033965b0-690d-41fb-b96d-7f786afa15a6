package com.thelastblockbender.bloodgrip;

import org.bukkit.FluidCollisionMode;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.BloodAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class BloodGrip extends BloodAbility implements AddonAbility {
  private long cooldown;
  private long duration;
  private double range;
  private double radius;
  private double speed;
  private double damage;

  private double darkenRange;
  private long darkenTransition;
  private long darkenDuration;
  private double darkenDamageFactor;

  private LivingEntity target;
  private boolean hasDarkness;
  private long time;
  private int lvl;

  public BloodGrip(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, BloodGrip.class) || !bPlayer.canBloodbend()) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("Abilities.Water.BloodGrip.Cooldown", currentLevel);
    duration = TLBMethods.getLong("Abilities.Water.BloodGrip.Duration", currentLevel);
    range = TLBMethods.getDouble("Abilities.Water.BloodGrip.Range", currentLevel);
    radius = TLBMethods.getDouble("Abilities.Water.BloodGrip.Radius", currentLevel);
    speed = TLBMethods.getDouble("Abilities.Water.BloodGrip.Speed", currentLevel);
    damage = TLBMethods.getDouble("Abilities.Water.BloodGrip.Damage", currentLevel);
    lvl = getConfig().getInt("Abilities.Water.BloodGrip.AffectOtherBloodBendersAfterLevel");

    darkenRange = TLBMethods.getDouble("Abilities.Water.BloodGrip.Darken.Range", currentLevel);
    darkenTransition = TLBMethods.getLong("Abilities.Water.BloodGrip.Darken.Transition", currentLevel);
    darkenDuration = TLBMethods.getLong("Abilities.Water.BloodGrip.Darken.Duration", currentLevel);
    darkenDamageFactor = TLBMethods.getDouble("Abilities.Water.BloodGrip.Darken.DamageFactor", currentLevel);

    time = System.currentTimeMillis();

    if (getTarget() instanceof LivingEntity living) {
      target = living;
      if (target instanceof Player otherPlayer) {
        var targetBPlayer = BendingPlayer.getBendingPlayer(otherPlayer);
        if (targetBPlayer != null && targetBPlayer.canBloodbend() && currentLevel <= lvl) {
          bPlayer.addCooldown(this);
          return;
        }
      }
      start();
    }
  }

  private Entity getTarget() {
    var origin = player.getEyeLocation();
    var dir = origin.getDirection();
    var r = NumberConversions.ceil(range);
    var mode = FluidCollisionMode.NEVER;
    var result = player.getWorld().rayTrace(origin, dir, r, mode, true, radius, this::isValidEntity);
    return result == null ? null : result.getHitEntity();
  }

  private boolean isValidEntity(Entity entity) {
    if (entity instanceof LivingEntity living) {
      if (living instanceof ArmorStand) {
        return false;
      }
      return !entity.getUniqueId().equals(player.getUniqueId());
    }
    return false;
  }

  private double tryGetDistanceSquared() {
    if (target == null || !target.isValid() || (target instanceof Player other && !(other.isOnline()))) {
      return -1;
    }
    if (!target.getWorld().equals(player.getWorld())) {
      return -1;
    }
    return player.getEyeLocation().distanceSquared(getCenterLoc(target));
  }

  @Override
  public void progress() {
    long currentTime = System.currentTimeMillis();
    if (!bPlayer.canBend(this) || !player.isSneaking() || currentTime > time + duration) {
      remove();
      return;
    }
    var distSq = tryGetDistanceSquared();
    var r = range + radius + (0.5 * target.getWidth());
    if (distSq < 0 || distSq > (r * r)) {
      remove();
      return;
    }
    if (distSq < darkenRange * darkenRange || currentTime > time + darkenTransition) {
      applyDarkness();
    }
    Vector gripDir = GeneralMethods.getDirection(getCenterLoc(target), player.getEyeLocation());
    target.setVelocity(gripDir.normalize().multiply(speed));
  }

  private void applyDarkness() {
    if (hasDarkness) {
      return;
    }
    int ticks = NumberConversions.ceil(darkenDuration / 50.0);
    target.addPotionEffect(new PotionEffect(PotionEffectType.DARKNESS, ticks, 0, true, false, false));
    hasDarkness = true;
  }

  @Override
  public void remove() {
    super.remove();
    if (!bPlayer.isOnCooldown(this)) {
      bPlayer.addCooldown(this);
    }
  }

  public void crush() {
    var dmg = hasDarkness ? damage * darkenDamageFactor : damage;
    DamageHandler.damageEntity(target, player, dmg, this);
    remove();
  }

  @Override
  public String getAuthor() {
    return "Prride and FriskAUs";
  }

  @Override
  public String getVersion() {
    return "Build v1.0";
  }

  @Override
  public String getDescription() {
    return "BloodGrip is the ability to grip onto your opponents and pull them towards you whilst doing damage. This is in the dark Bloodbending category, making it one of the powerful, deadly, yet dark moves.";
  }

  @Override
  public String getInstructions() {
    return "\n(Grip)\nHold sneak on an entity to slowly pull them closer to you.\n(Crush)\nLeft click while pulling the entity to deal damage to them.";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new BloodGripListener(), ProjectKorra.plugin);

    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Cooldown", 7000);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Duration", 5000);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Range", 7);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Radius", 0.5);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Speed", 0.2);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Damage", 1.35);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.AffectOtherBloodBendersAfterLevel", 5);

    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Darken.Range", 5);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Darken.Transition", 750);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Darken.Duration", 5500);
    ConfigManager.getConfig().addDefault("Abilities.Water.BloodGrip.Darken.DamageFactor", 1.75);

    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded! ");
  }

  @Override
  public void stop() {
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return getCenterLoc(target == null ? player : target);
  }

  @Override
  public String getName() {
    return "BloodGrip";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  private static Location getCenterLoc(Entity entity) {
    return entity.getLocation().add(0, 0.5 * entity.getHeight(), 0);
  }
}
