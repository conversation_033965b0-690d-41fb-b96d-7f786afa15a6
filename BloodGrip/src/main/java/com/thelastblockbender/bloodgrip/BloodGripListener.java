package com.thelastblockbender.bloodgrip;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class BloodGripListener implements Listener {
  @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
  public void onSneak(PlayerToggleSneakEvent event) {
    if (!event.isSneaking()) {
      return;
    }
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("BloodGrip")) {
      new BloodGrip(event.getPlayer());
    }
  }

  @EventHandler(priority = EventPriority.MONITOR)
  public void onPlayerDamage(EntityDamageByEntityEvent event) {
    if (event.getEntity() instanceof Player player) {
      BloodGrip ability = CoreAbility.getAbility(player, BloodGrip.class);
      if (ability != null) {
        ability.getBendingPlayer().addCooldown(ability);
        ability.remove();
        ParticleEffect.WATER_DROP.display(player.getLocation().add(0, 1.6, 0), 25, 0.5D, 0.5D, 0.5D, 0.1D);
      }
    }
  }

  @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
  public void onSwing(PlayerAnimationEvent event) {
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("BloodGrip")) {
      BloodGrip ability = CoreAbility.getAbility(event.getPlayer(), BloodGrip.class);
      if (ability != null) {
        ability.crush();
      }
    }
  }
}
