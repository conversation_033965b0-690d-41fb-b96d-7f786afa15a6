plugins {
    java
}
subprojects {
    group = "com.thelastblockbender"
    version = "1.2.1"
    pluginManager.apply("java")
    pluginManager.apply("eclipse")

    repositories {
        mavenCentral()
        maven("https://oss.sonatype.org/content/repositories/snapshots")
        maven("https://repo.papermc.io/repository/maven-public/")
        maven("https://maven.enginehub.org/repo/")
        maven {
            name = "denizen-repo"
            url = uri("https://maven.citizensnpcs.co/repo")
        }

        maven {
            name = "tlb"
            url = uri("https://repo.thelastblockbender.com/private/")
            credentials(PasswordCredentials::class)
        }
        flatDir { dirs("$rootDir/libs") }
    }
    plugins.withId("java") {
        the<JavaPluginExtension>().toolchain {
            languageVersion.set(JavaLanguageVersion.of(21))
        }
    }
    dependencies {
        compileOnly("io.papermc.paper:paper-api:1.21.4-R0.1-SNAPSHOT")
        compileOnly("com.projectkorra", "ProjectKorra", "1.9.3")
        compileOnly("me.moros", "Hyperion", "1.7.0")
        compileOnly("com.jedk1", "Jedcore", "2.10.0")
        compileOnly("com.sk89q.worldedit:worldedit-bukkit:7.2.15")

        // Add Denizen and Citizens only for projects that need it
        if (project.name == "SpiritProjection") {
            compileOnly("com.denizenscript:denizen:1.3.0-SNAPSHOT")
            compileOnly("net.citizensnpcs:citizens-main:2.0.33-SNAPSHOT") {
                exclude(group = "*", module = "*")
            }
        }


    }
    tasks.jar {
        //destinationDirectory.set(file(rootProject.buildDir))
        destinationDirectory.set(file("../../../Desktop/TLB/1.21/plugins/ProjectKorra/Abilities"))
    }
}


//    project.buildDir = dir("~/Desktop/TLB/1.21/plugins/ProjectKorra/Abilities")
