package com.thelastblockbender.rootspear;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class RootSpearListener implements Listener {

	@EventHandler
	public void onPlayerInteract(PlayerInteractEvent event) {
		if (event.getAction() != Action.LEFT_CLICK_AIR && event.getAction() != Action.LEFT_CLICK_BLOCK) {
			return;
		}
		
		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		
		if (bPlayer == null) {
			return;
		}
		
		// Check if the player has RootSpear selected
		if (!bPlayer.getBoundAbilityName().equalsIgnoreCase("RootSpear")) {
			return;
		}
		
		// Check if the player has an active RootSpear instance
		RootSpear rootSpear = CoreAbility.getAbility(player, RootSpear.class);
		if (rootSpear != null) {
			rootSpear.launchSpear();
		} else if (bPlayer.canBend(CoreAbility.getAbility("RootSpear"))) {
			// If they don't have an active instance but can use the ability,
			// create a new one and immediately launch it
			RootSpear newSpear = new RootSpear(player);
			if (CoreAbility.getAbility(player, RootSpear.class) != null) {
				newSpear.launchSpear();
			}
		}
	}

	@EventHandler
	public void onSneak(PlayerToggleSneakEvent event) {
		if (!event.isSneaking()) {
			return;
		}
		
		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		
		if (bPlayer == null) {
			return;
		}
		
		// Check if the player has RootSpear selected and doesn't already have an active instance
		if (bPlayer.getBoundAbilityName().equalsIgnoreCase("RootSpear") && 
			!CoreAbility.hasAbility(player, RootSpear.class) && 
			bPlayer.canBend(CoreAbility.getAbility("RootSpear"))) {
			
			RootSpear spear = new RootSpear(player);
			
		}
	}
}
