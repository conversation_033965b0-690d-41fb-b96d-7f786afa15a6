package com.thelastblockbender.roll;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ActionBar;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class Roll extends ChiAbility implements AddonAbility {
  private double dodgePower;
  private double wooshRadius;
  private long cooldown;
  private int durationTicks;

  private int ticks;

  private Vector direction;

  public Roll(Player player) {
    super(player);

    if (!bPlayer.canBend(this)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    dodgePower = TLBMethods.getDouble("Abilities.Chi.Roll.DodgePower", currentLevel);
    wooshRadius = TLBMethods.getDouble("Abilities.Chi.Roll.WooshRadius", currentLevel);
    cooldown = TLBMethods.getLong("Abilities.Chi.Roll.Cooldown", currentLevel);
    durationTicks = TLBMethods.getInt("Abilities.Chi.Roll.DurationTicks", currentLevel);

    if (player.isOnGround()) {
      bPlayer.addCooldown(this, cooldown);
      direction = player.getEyeLocation().getDirection().setY(0).normalize();
      start();
    }
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreBindsCooldowns(this) || ++ticks > durationTicks) {
      remove();
      return;
    }

    Location center = player.getLocation().add(0, 0.2, 0);
    player.playSound(center, Sound.ENTITY_PLAYER_ATTACK_SWEEP, 2, 0.1F);
    player.setVelocity(direction.clone().multiply(dodgePower));
    ParticleEffect.CRIT_MAGIC.display(center, 4, 0.2, 0.2, 0.2, 0.02);
    player.setFireTicks(-1);

    for (final Block block : GeneralMethods.getBlocksAroundPoint(player.getLocation(), wooshRadius)) {
      final Material material = block.getType();
      if (isFire(material) && !GeneralMethods.isRegionProtectedFromBuild(this, block.getLocation())) {
        ParticleEffect.CLOUD.display(player.getLocation().add(player.getLocation().getDirection().normalize().multiply(-1)), 3, 0.2, 0.2, 0.2, 0.02);
        block.setType(Material.AIR);
        block.getWorld().playEffect(block.getLocation(), Effect.EXTINGUISH, 0);
      }
    }
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public String getName() {
    return "Roll";
  }

  @Override
  public String getDescription() {
    return "Use this move to quickly dodge an attack or to break your fall in a pinch!";
  }

  @Override
  public String getInstructions() {
    return "Left click for a short dodge in the direction you're looking to avoid an attack. While falling, hold shift to break your fall and reduce damage taken based on how far you fall from.";
  }

  @Override
  public boolean isHarmlessAbility() {
    return true;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new RollListener(), ProjectKorra.plugin);

    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.DodgePower", 0.5);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.WooshRadius", 2);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.DurationTicks", 4);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.Passive.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.Passive.FallFactor", 0.9);
    ConfigManager.getConfig().addDefault("Abilities.Chi.Roll.Passive.MaxHeightToCompletelyNegate", 10);
    ConfigManager.defaultConfig.save();
  }

  public void stop() {
    super.remove();
  }

  @Override
  public Location getLocation() {
    return player.getLocation();
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  public static double handleFallDamage(BendingPlayer bPlayer, double oldDamage) {
    int statLevel = StatisticsMethods.getId("AbilityLevel_Roll");
    long currentLevel = TLBMethods.limitLevels(bPlayer.getPlayer(), statLevel);

    long cooldown = TLBMethods.getLong("Abilities.Chi.Roll.Passive.Cooldown", currentLevel);
    double fallFactor = TLBMethods.getDouble("Abilities.Chi.Roll.Passive.FallFactor", currentLevel);
    float maxHeight = (float) TLBMethods.getDouble("Abilities.Chi.Roll.Passive.MaxHeightToCompletelyNegate", currentLevel);

    bPlayer.addCooldown("Roll", cooldown);
    

    String message = "\u00a76\u00a7lROLLED";
    ActionBar.sendActionBar(message, (Player[])new Player[]{bPlayer.getPlayer()});
    return bPlayer.getPlayer().getFallDistance() <= maxHeight ? 0 : oldDamage * fallFactor;
  }
}

