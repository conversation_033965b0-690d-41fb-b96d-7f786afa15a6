package com.thelastblockbender.roll;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDamageEvent.DamageCause;
import org.bukkit.event.player.PlayerAnimationEvent;

public class RollListener implements Listener {
  @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
  public void onSwing(PlayerAnimationEvent event) {
    Player player = event.getPlayer();
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("Roll")) {
      new Roll(player);
    }
  }

  @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
  public void onFall(final EntityDamageEvent event) {
    if (event.getCause() == DamageCause.FALL && event.getEntity() instanceof Player player) {
      if (event.getDamage() <= 0 || !player.isSneaking()) {
        return;
      }
      BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
      if (bPlayer.hasElement(Element.CHI) && bPlayer.getBoundAbilityName().equalsIgnoreCase("Roll")) {
        if (bPlayer.canBendIgnoreBinds(CoreAbility.getAbility(Roll.class))) {
          double oldDamage = event.getDamage();
          double newDamage = Roll.handleFallDamage(bPlayer, oldDamage);
          if (newDamage <= 0) {
            event.setCancelled(true);
          } else if (oldDamage != newDamage) {
            event.setDamage(newDamage);
          }
        }
      }
    }
  }
}

