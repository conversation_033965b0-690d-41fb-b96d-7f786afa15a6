package me.Pride.korra.Blossom;

import java.util.*;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.BlockState;
import org.bukkit.block.data.Ageable;
import org.bukkit.block.data.BlockData;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.block.BlockGrowEvent;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.ice.PhaseChange;

public class Blossom extends PlantAbility implements AddonAbility {
	
	private static String path = "ExtraAbilities.Prride.Blossom.";
	FileConfiguration config = ConfigManager.getConfig();
	
	private long cooldown;
	private long duration;
	private int radius;
	private long revertTime;
	private boolean reversible;
	private int growthSpeed;
	private boolean donework = false;
	
	public long currentLevel;

	private static List<Block> plantBlocks = new ArrayList<>();
	
	Random rand = new Random();
	private final static List<Material> grassplants = Arrays.asList(Material.SHORT_GRASS, Material.SHORT_GRASS, Material.SHORT_GRASS,  Material.POPPY, Material.DANDELION);
	private final static List<Material> netherPlants = Arrays.asList(Material.CRIMSON_FUNGUS, Material.WARPED_FUNGUS, Material.WARPED_ROOTS,  Material.NETHER_WART, Material.CRIMSON_ROOTS, Material.WEEPING_VINES);
	private final static List<Material> strongPlants = Arrays.asList(Material.SHORT_GRASS, Material.SHORT_GRASS, Material.SHORT_GRASS,  Material.POPPY, Material.DANDELION, Material.WHITE_TULIP, Material.PINK_TULIP, Material.RED_TULIP, Material.ORANGE_TULIP, Material.AZURE_BLUET, Material.BLUE_ORCHID, Material.BAMBOO_SAPLING, Material.DARK_OAK_SAPLING, Material.LILY_OF_THE_VALLEY, Material.WITHER_ROSE, 
			Material.SHORT_GRASS, Material.SHORT_GRASS, Material.SHORT_GRASS,  Material.POPPY, Material.DANDELION, Material.WHITE_TULIP, Material.PINK_TULIP, Material.RED_TULIP, Material.ORANGE_TULIP, Material.AZURE_BLUET, Material.BLUE_ORCHID, Material.BAMBOO_SAPLING, Material.DARK_OAK_SAPLING, Material.LILY_OF_THE_VALLEY);
	
	public Blossom(Player player) {
		super(player);
		
		if (!bPlayer.canPlantbend()) {
			return;
		}
		
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		currentLevel = TLBMethods.limitLevels(player, statLevel);
		
		cooldown = TLBMethods.getLong(path + "Cooldown", currentLevel);
		duration = TLBMethods.getLong(path + "Duration", currentLevel);
		radius = TLBMethods.getInt(path + "Radius", currentLevel);
		revertTime = TLBMethods.getLong(path + "RevertTime", currentLevel);
		growthSpeed = TLBMethods.getInt(path + "GrowthSpeed", currentLevel);

	
		start();
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public String getName() {
		return "Blossom";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return true;
	}

	@Override
	public void progress() {
		if (player.isDead() || !player.isOnline()) {
			remove();
			return;
		}
		
		if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
			bPlayer.addCooldown(this);
			remove();
			return;
		}

		if(this.getStartTime() + duration < System.currentTimeMillis()){
			if (donework) {
				bPlayer.addCooldown(this);
			}
			remove();
			return;
		}
		
		if (!player.isSneaking()) {
			if (donework) {
				bPlayer.addCooldown(this);
			}
			remove();
			return;
		}

		blossom();
	}

	private void growPlant(Block cropBlock) {
		
		Material mat = cropBlock.getType();
        BlockData data = cropBlock.getBlockData();

        switch (mat) {
        case PUMPKIN_STEM:
        case MELON_STEM:
        case CARROTS:
        case WHEAT:
        case POTATOES:
        case COCOA:
        case NETHER_WART:
        case BEETROOTS:

            BlockData cropState = cropBlock.getBlockData();
            if (cropState instanceof Ageable) {
                Ageable ag = (Ageable) cropState;
                if (ag.getAge() >= ag.getMaximumAge()) {
                    return;
                }
                ag.setAge(ag.getAge() + 1);
                data = ag;
            }
            break;
        case CACTUS:
        case SUGAR_CANE:
            int height = 1;
            if (cropBlock.getRelative(BlockFace.DOWN).getType() == mat) { // Only grow if argument is the base
                // block
                return;
            }
            while ((cropBlock = cropBlock.getRelative(BlockFace.UP)).getType() == mat) {
                if (++height >= 3) { // Cancel if cactus/cane is fully grown
                    return;
                }
            }
            //if (!airs().contains(cropBlock.getType())) { // Only grow if argument is the base block
            //    return;
            //}

            break;
        default:
            return;
        }

        BlockState bs = cropBlock.getState();
        bs.setType(mat);
        BlockGrowEvent evt = new BlockGrowEvent(cropBlock, bs);
        Bukkit.getPluginManager().callEvent(evt);
        if (!evt.isCancelled()) {
            cropBlock.setType(mat);
            cropBlock.setBlockData(data);

        }
        return;
	}
	
	private void blossom() {

		
		for (int i = 0; i < growthSpeed; i++) {
			Location loc = player.getLocation();
			loc.add((rand.nextBoolean() ? 1 : -1) * rand.nextInt(radius), 0, (rand.nextBoolean() ? 1 : -1) * rand.nextInt(radius));
			Block baseblock = GeneralMethods.getTopBlock(loc, 3);
			Block plantblock = baseblock.getRelative(BlockFace.UP);

			if (GeneralMethods.isRegionProtectedFromBuild(player, "Blossom", plantblock.getLocation())) {
				continue;
			}
			
			if (baseblock.getBlockData() instanceof Ageable && !TempBlock.isTempBlock(baseblock)) {
            	growPlant(baseblock);
            }
			
			if (isAir(baseblock.getType()) || !isAir(plantblock.getType())){
				continue;
			}
			
			
			Material plant;
			if (baseblock.getType() == Material.GRASS_BLOCK) {
				if (currentLevel > 8) {
					plant = strongPlants.get(rand.nextInt(strongPlants.size()));
				}
				else {
					plant = grassplants.get(rand.nextInt(grassplants.size()));
				}
			}
			else if (baseblock.getType() == Material.CRIMSON_NYLIUM || baseblock.getType() == Material.WARPED_NYLIUM) {
				plant = netherPlants.get(rand.nextInt(netherPlants.size()));
			} else if (isMushroomBase(baseblock.getType())) {
				plant = rand.nextBoolean() ? Material.BROWN_MUSHROOM : Material.RED_MUSHROOM;
			} else {
				continue;
			}

				Random rn = new Random();
				int scale = rn.nextInt(10) + 1;
				
				if (scale >= 7 || this.currentLevel >= 5) {	
					if (plant == Material.WITHER_ROSE) {
						TempBlock tempBlock = new TempBlock(plantblock, plant.createBlockData(), revertTime/4);
						PhaseChange.getFrozenBlocksMap().put(tempBlock, player);
					}
					else {
						TempBlock tempBlock = new TempBlock(plantblock, plant.createBlockData(), revertTime);
						PhaseChange.getFrozenBlocksMap().put(tempBlock, player);
					}
				}
			
			donework = true;

			ParticleEffect.VILLAGER_HAPPY.display(plantblock.getLocation(), 3, 0.2F, 0.2F, 0.2F, 0.2F);
		}
		
		
	}
	
	private boolean isMushroomBase(Material type){
		switch(type){
			case MYCELIUM:
			case SOUL_SAND:
			case PODZOL:
			case GRAVEL:
				return true;
		}
		return false;
	}

	@Override
	public String getDescription() {
		return Element.WATER.getColor() + "Waterbenders are able to redirect energy paths within plants in order to initiate plant growth "
				+ "and cause deep roots of trees to grow flowers from underground! When used on mycelium and soul sand, these plants will "
				+ "blossom into mushrooms.";
	}
	
	@Override
	public String getInstructions() {
		return ChatColor.GOLD + "To use, hold sneak to cause plants and flowers to bloom.";
	}

	@Override
	public String getAuthor() {
		return Element.WATER.getColor() + "" + ChatColor.UNDERLINE + 
				"Prride, LiamRP, Shookified and PhanaticD";
	}

	@Override
	public String getVersion() {
		return Element.WATER.getColor() + "" + ChatColor.UNDERLINE + 
				"VERSION 1";
	}
	
	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new BlossomListener(), ProjectKorra.plugin);	
		ProjectKorra.log.info(getName() + " by " + getAuthor() + " " + getVersion() + " loaded!");
		
		ConfigManager.getConfig().addDefault(path + "Cooldown", 6500);
		ConfigManager.getConfig().addDefault(path + "Duration", 2000);
		ConfigManager.getConfig().addDefault(path + "Radius", 5);
		ConfigManager.getConfig().addDefault(path + "PlantsRevert", false);
		ConfigManager.getConfig().addDefault(path + "RevertTime", 20000);
		ConfigManager.getConfig().addDefault(path + "GrowthSpeed", 1);
		ConfigManager.defaultConfig.save();
	}

	@Override
	public void stop() {
		ProjectKorra.log.info(getName() + " by " + getAuthor() + " " + getVersion() + " stopped!");
		super.remove();
	}

}
