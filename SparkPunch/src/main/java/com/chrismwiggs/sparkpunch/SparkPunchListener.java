package com.chrismwiggs.sparkpunch;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

import com.projectkorra.projectkorra.BendingPlayer;

public class SparkPunchListener implements Listener {
  @EventHandler
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    }
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("SparkPunch")) {
      new SparkPunch(event.getPlayer());
    }
  }
}
