package com.chrismwiggs.sparkpunch;

import java.util.Collection;
import java.util.HashSet;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.LightningAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.MovementHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.ChatColor;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Particle.DustTransition;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class SparkPunch extends LightningAbility implements AddonAbility {
  private static final DustTransition DATA = new DustTransition(fromHex("93AFBF"), fromHex("3157B0"), 1);
  private Location location;
  private Location origin;
  private Vector direction;
  private long cooldown;
  private double range;
  private double speed;
  private double damage;
  private double collisionRadius;
  private double effectRadius;
  private boolean controllable;
  private long stunDuration;

  public SparkPunch(Player player) {
    super(player);
    if (!bPlayer.canBend(this) || !bPlayer.canCurrentlyBendWithWeapons()) {
      return;
    }
    setFields();
    if (!GeneralMethods.isSolid(location.getBlock())) {
      start();
      bPlayer.addCooldown(this);
    }
  }

  private void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);
    this.cooldown = TLBMethods.getLong("ExtraAbilities.Lightning.SparkPunch.Cooldown", currentLevel);
    this.stunDuration = TLBMethods.getLong("ExtraAbilities.Lightning.SparkPunch.StunDuration", currentLevel);
    this.range = TLBMethods.getDouble("ExtraAbilities.Lightning.SparkPunch.Range", currentLevel);
    this.speed = TLBMethods.getDouble("ExtraAbilities.Lightning.SparkPunch.Speed", currentLevel);
    this.damage = TLBMethods.getDouble("ExtraAbilities.Lightning.SparkPunch.Damage", currentLevel);
    this.collisionRadius = TLBMethods.getDouble("ExtraAbilities.Lightning.SparkPunch.CollisionRadius", currentLevel);
    this.effectRadius = TLBMethods.getDouble("ExtraAbilities.Lightning.SparkPunch.EffectRadius", currentLevel);
    this.controllable = ConfigManager.getConfig().getBoolean("ExtraAbilities.Lightning.SparkPunch.Controllable");

    this.origin = this.player.getLocation().add(0, 1, 0);
    this.location = this.origin.clone();
    this.direction = this.player.getLocation().getDirection();
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreCooldowns(this) || location.distanceSquared(origin) > range * range) {
      remove();
      return;
    }
    Vector dir = controllable ? player.getLocation().getDirection() : direction;
    dir = dir.clone().multiply(speed);
    if (GeneralMethods.checkDiagonalWall(location, dir)) {
      remove();
      return;
    }
    location.add(dir);
    if (GeneralMethods.isSolid(location.getBlock())) {
      remove();
      return;
    }
    ParticleEffect.END_ROD.display(location, 1, 0.1, 0.1, 0.1, 0);
    spawnParticles(location, 0.2, 16);
    location.getWorld().playSound(location, Sound.ENTITY_EVOKER_CAST_SPELL, 1, 2);
    boolean hit = false;
    Collection<LivingEntity> entities = new HashSet<>();
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(location, effectRadius)) {
      if (entity.getUniqueId() != player.getUniqueId() && entity instanceof LivingEntity living) {
        entities.add(living);
        if (!hit && entity.getBoundingBox().expand(collisionRadius).contains(location.toVector())) {
          hit = true;
        }
      }
    }

    if (hit) {
      int counter = 0;
      for (LivingEntity entity : entities) {
        DamageHandler.damageEntity(entity, damage, this);
        if (++counter < 8) {
          spawnParticles(entity.getLocation().add(0, 0.5 * entity.getHeight(), 0), 0.3, 8);
        }
        new MovementHandler(entity, this)
          .stopWithDuration(stunDuration / 1000 * 20, ChatColor.RED + "* Electrocuted *");
      }
      remove();
    }
  }

  private void spawnParticles(Location loc, double offset, int amount) {
    Particle.DUST_COLOR_TRANSITION.builder().location(loc).count(amount)
      .offset(offset, offset, offset).extra(0).data(DATA).force(true).spawn();
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "SparkPunch";
  }

  @Override
  public String getDescription() {
    return "Concentrate electricity in your fist and punch your enemies with sparks!";
  }

  @Override
  public String getInstructions() {
    return "Use left-click!";
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SparkPunchListener(), ProjectKorra.plugin);
    ProjectKorra.log.info("Successfully enabled " + getName() + " by " + getAuthor());

    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.Cooldown", 2000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.StunDuration", 500);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.Range", 3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.Speed", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.Damage", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.CollisionRadius", 0.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.EffectRadius", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Lightning.SparkPunch.Controllable", true);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info("Successfully disabled " + getName() + " by " + getAuthor());
    super.remove();
  }

  @Override
  public boolean isExplosiveAbility() {
    return false;
  }

  @Override
  public boolean isIgniteAbility() {
    return false;
  }

  private static Color fromHex(String hexVal) {
    int r = 0;
    int g = 0;
    int b = 0;
    if (hexVal.length() <= 6) {
      r = Integer.valueOf(hexVal.substring(0, 2), 16);
      g = Integer.valueOf(hexVal.substring(2, 4), 16);
      b = Integer.valueOf(hexVal.substring(4, 6), 16);
    }
    return Color.fromRGB(r, g, b);
  }
}
