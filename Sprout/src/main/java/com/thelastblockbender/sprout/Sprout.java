package com.thelastblockbender.sprout;

import java.util.ArrayList;
import java.util.List;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.ice.PhaseChange;

import me.moros.hyperion.methods.CoreMethods;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class Sprout extends PlantAbility implements AddonAbility {
  private Location location;
  private Vector direction;
  private BlockData data;

  private double damage;
  private long cooldown;
  private double range;
  private double speed;
  private double collisionRadius;
  private int effectDuration;
  private int effectAmplifier;
  private int mossDuration;

	public static List<TempBlock> tempSources = new ArrayList<>();

  private double distanceTravelled;

  public Sprout(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, Sprout.class)) {
      return;
    }

    setFields();
    if (prepare()) {
      bPlayer.addCooldown(this);
      start();
    }
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    damage = TLBMethods.getDouble("ExtraAbilities.NickC1211.Sprout.Damage", currentLevel);
    cooldown = TLBMethods.getLong("ExtraAbilities.NickC1211.Sprout.Cooldown", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.NickC1211.Sprout.Range", currentLevel);
    speed = TLBMethods.getDouble("ExtraAbilities.NickC1211.Sprout.Speed", currentLevel);
    collisionRadius = TLBMethods.getDouble("ExtraAbilities.NickC1211.Sprout.CollisionRadius", currentLevel);
    effectDuration = TLBMethods.getInt("ExtraAbilities.NickC1211.Sprout.EffectDuration", currentLevel);
    effectAmplifier = TLBMethods.getInt("ExtraAbilities.NickC1211.Sprout.EffectAmplifier", currentLevel) - 1;
    mossDuration = TLBMethods.getInt("ExtraAbilities.NickC1211.Sprout.MossDuration", currentLevel);
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreCooldowns(this) || !player.isSneaking()) {
      remove();
      resetBlocks();
      return;
    }
    calculateDirection();
    data = getData(location.getBlock().getRelative(BlockFace.DOWN));
    if (!advanceLocation()) {
      remove();
      return;
    }
    checkDamage();
  }

  private boolean advanceLocation() {
    if (location.getBlock().isLiquid() || GeneralMethods.checkDiagonalWall(location, direction)) {
      return false;
    }
    location.add(direction);
    spawnParticles(location, data);
    location.getWorld().playSound(location, Sound.BLOCK_GRASS_BREAK, 1, 1);
    distanceTravelled += speed;
    Block baseBlock = location.getBlock().getRelative(BlockFace.DOWN);
    if (!isValidBlock(baseBlock)) {
      if (isValidBlock(baseBlock.getRelative(BlockFace.UP))) {
        location.add(0, 1, 0);
      } else if (isValidBlock(baseBlock.getRelative(BlockFace.DOWN))) {
        location.add(0, -1, 0);
      } else {
        return false;
      }
    }
    return !GeneralMethods.isRegionProtectedFromBuild(this, location) && distanceTravelled < range;
  }

  private void checkDamage() {
    boolean hasHit = false;
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(location, collisionRadius)) {
      if (entity.getEntityId() != player.getEntityId() && entity instanceof LivingEntity living) {
        Location target = entity.getLocation().add(0, 0.3, 0);
        spawnParticles(target, data);
        spawnParticles(target.add(0, 0.5, 0), data);
        DamageHandler.damageEntity(living, damage, this);
        int duration = effectDuration / 1000 * 20;
        living.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, duration, effectAmplifier));
        hasHit = true;
      }
    }
    if (hasHit) {
      remove();
    }
  }

  public boolean prepare() {
    location = player.getLocation();
    Block below = location.getBlock().getRelative(BlockFace.DOWN);
    if (!isGroundPlant(below) || below.getType() == Material.DIRT_PATH) {
      return false;
    }
    Block block = location.getBlock();
    if (block.isLiquid() || !isTransparent(block)) {
      return false;
    }
    location.setY(block.getY() + 0.3);
    return true;
  }

  private boolean isValidBlock(Block block) {
    if (!isTransparent(block.getRelative(BlockFace.UP))) return false;
    if (block.isLiquid()) return false;
    if (block.getType() == Material.DIRT_PATH) return true;
    return isGroundPlant(block);
  }

  private void calculateDirection() {
    Entity targetedEntity = GeneralMethods.getTargetedEntity(player, range, List.of(player));
    Location end;
    if (targetedEntity instanceof LivingEntity && targetedEntity.getLocation().distanceSquared(location) <= range * range) {
      end = targetedEntity.getLocation();
    } else {
      end = GeneralMethods.getTargetedLocation(player, range);
    }
    direction = CoreMethods.calculateFlatVector(location, end).multiply(speed);
  }

  private void spawnParticles(Location loc, BlockData data) {
    Particle.BLOCK_MARKER.builder().location(loc).count(3)
      .offset(0.1, 0.1, 0.1).extra(0).data(data).force(true).spawn();
    ParticleEffect.VILLAGER_HAPPY.display(loc, 2, 0.5F, 0.5F, 0.5F, 0.0F);
    if (location.getBlock().getRelative(BlockFace.DOWN).getType() == Material.GRASS_BLOCK) {
     TempBlock tb = new TempBlock(location.getBlock().getRelative(BlockFace.DOWN), Material.MOSS_BLOCK.createBlockData(), mossDuration);
     PhaseChange.getFrozenBlocksMap().put(tb, player);
     tempSources.add(tb);
    }
  }

  private BlockData getData(Block block) {
    return switch (block.getType()) {
      case PODZOL, ROOTED_DIRT, MUSHROOM_STEM -> Material.HANGING_ROOTS.createBlockData();
      case WARPED_NYLIUM -> Material.WARPED_ROOTS.createBlockData();
      case RED_MUSHROOM_BLOCK, CRIMSON_NYLIUM -> Material.CRIMSON_ROOTS.createBlockData();
      default -> Material.SEAGRASS.createBlockData();
    };
  }
 
  public void resetBlocks() {
    new BukkitRunnable(){
      public void run(){
        for (final TempBlock tb : tempSources) {
          PhaseChange.getFrozenBlocksMap().remove(tb, player);
        }
        tempSources.clear();
      }
  }.runTaskLater(ProjectKorra.plugin, 70);
    
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "Sprout";
  }

  @Override
  public String getDescription() {
    return "This ability allows skilled plantbenders to bring up the roots underground and shoot them at their opponent. This moves requires that you be standing on fertile ground (grass blocks). ";
  }

  @Override
  public String getInstructions() {
    return "Hold Shift and Left Click to shoot, and move cursor side to side to aim.";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "v1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SproutListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + "by" + getAuthor() + "loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.Cooldown", 6000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.Range", 10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.Damage", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.CollisionRadius", 0.8);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.Speed", 0.8);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.EffectDuration", 5000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.EffectAmplifier", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Sprout.MossDuration", 1800);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + "by" + getAuthor() + "disabled!");
  }
}
