package com.thelastblockbender.sprout;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.player.PlayerAnimationEvent;

public class SproutListener implements org.bukkit.event.Listener {
  @EventHandler
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    }
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("Sprout")) {
      new Sprout(event.getPlayer());
    }
  }
}
