package me.seabarrel.earthsculpture;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.data.BlockData;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

public class EarthSculpture
extends EarthAbility
implements AddonAbility {
    @Attribute(value="Cooldown")
    private long cooldown;
    @Attribute(value="SelectRange")
    private int selectRange;
    private Block target;

    public EarthSculpture(Player player) {
        super(player);
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);
        this.cooldown = TLBMethods.getLong("ExtraAbilities.Seabarrel.EarthSculpture.Cooldown", currentLevel);
        this.selectRange = TLBMethods.getInt("ExtraAbilities.Seabarrel.EarthSculpture.SelectRange", currentLevel);
        if (!this.bPlayer.canBend(this)) {
            return;
        }
        if (this.prepare()) {
            this.start();
        } else {
            this.remove();
        }
    }

    @Override
    public void progress() {
        Material old = this.target.getType();
        Material newType = this.getNewBlock();
        if (newType == null) {
            this.remove();
            return;
        }
        BlockData oldData = this.target.getState().getBlockData();
        BlockData newData = Bukkit.createBlockData((String)oldData.getAsString().replace(old.toString().toLowerCase(), newType.toString().toLowerCase()));
        this.target.setBlockData(newData);
        this.playParticles(newType);
        EarthSculpture.playEarthbendingSound(this.getLocation());
        this.bPlayer.addCooldown((Ability)this, this.getCooldown());
        this.remove();
    }

    public Material getNewBlock() {
        FileConfiguration config = ConfigManager.getConfig();
        for (String cycle : config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycles")) {
            int i;
            int total;
            int index = 0;
            for (Object block : config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks")) {
                if (this.getMaterial((String)block) != null && this.getMaterial((String)block) == this.target.getType()) {
                    if (index == config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size() - 1) {
                        index = -1;
                    }
                    return this.getMaterial((String)config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").get(index + 1));
                }
                ++index;
            }
            index = 0;
            if (config.getBoolean("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Stairs")) {
                for (Object block : config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks")) {
                    if (this.getMaterial((String)(block = (String)block + "_STAIRS")) != null && this.getMaterial((String)block) == this.target.getType()) {
                        total = 0;
                        for (i = index; i < config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size(); ++i) {
                            if (i == config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size() - 1) {
                                i = -1;
                            }
                            if (this.getMaterial((String)config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").get(i + 1) + "_STAIRS") != null) {
                                return this.getMaterial((String)config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").get(i + 1) + "_STAIRS");
                            }
                            if (++total > config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size()) break;
                        }
                    }
                    ++index;
                }
            }
            index = 0;
            if (!config.getBoolean("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Slab")) continue;
            for (Object block : config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks")) {
                if (this.getMaterial((String)(block = (String)block + "_SLAB")) != null && this.getMaterial((String)block) == this.target.getType()) {
                    total = 0;
                    for (i = index; i < config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size(); ++i) {
                        if (i == config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size() - 1) {
                            i = -1;
                        }
                        if (this.getMaterial((String)config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").get(i + 1) + "_SLAB") != null) {
                            return this.getMaterial((String)config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").get(i + 1) + "_SLAB");
                        }
                        if (++total > config.getStringList("ExtraAbilities.Seabarrel.EarthSculpture.Cycle." + cycle + ".Blocks").size()) break;
                    }
                }
                ++index;
            }
        }
        return null;
    }

    public void playParticles(Material material) {
        ParticleEffect.BLOCK_CRACK.display(this.target.getLocation().add(0.5, 0.5, 0.5), 10, 0.4, 0.4, 0.4, (Object)material.createBlockData());
    }

    public boolean prepare() {
        this.target = this.player.getTargetBlock(EarthSculpture.getTransparentMaterialSet(), this.selectRange);
        if (this.target == null) {
            return false;
        }
        if (GeneralMethods.isRegionProtectedFromBuild(this.player, this.target.getLocation())) {
            return false;
        }
        if (TempBlock.isTempBlock(this.target)) {
            return false;
        }
        return (!EarthSculpture.isMetal(this.target) || this.bPlayer.hasSubElement(Element.METAL)) && (!EarthSculpture.isSand(this.target) || this.bPlayer.hasSubElement(Element.SAND));
    }

    public Material getMaterial(String mat) {
        if (Material.getMaterial((String)mat) == null) {
            if (mat.toLowerCase().contains("slab") && mat.toLowerCase().contains("bricks") && Material.getMaterial((String)mat.replace("BRICKS", "BRICK")) == null) {
                return null;
            }
            return Material.getMaterial((String)mat.replace("BRICKS", "BRICK"));
        }
        return Material.getMaterial((String)mat);
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public long getCooldown() {
        return this.cooldown;
    }

    @Override
    public String getName() {
        return "EarthSculpture";
    }

    @Override
    public Location getLocation() {
        return this.player.getLocation();
    }

    @Override
    public void load() {
        FileConfiguration config = ConfigManager.getConfig();
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cooldown", (Object)1000);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.SelectRange", (Object)5);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycles", (Object)new String[]{"Sand", "RedSand", "Stone", "Andesite", "Granite", "Diorite"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Sand.Stairs", (Object)false);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Sand.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Sand.Blocks", (Object)new String[]{"SAND", "SANDSTONE", "CUT_SANDSTONE", "CHISELED_SANDSTONE"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.RedSand.Stairs", (Object)false);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.RedSand.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.RedSand.Blocks", (Object)new String[]{"RED_SAND", "RED_SANDSTONE", "CUT_RED_SANDSTONE", "CHISELED_RED_SANDSTONE"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Stone.Stairs", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Stone.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Stone.Blocks", (Object)new String[]{"STONE", "STONE_BRICKS", "CHISELED_STONE_BRICKS"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Andesite.Stairs", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Andesite.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Andesite.Blocks", (Object)new String[]{"ANDESITE", "POLISHED_ANDESITE"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Granite.Stairs", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Granite.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Granite.Blocks", (Object)new String[]{"GRANITE", "POLISHED_GRANITE"});
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Diorite.Stairs", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Diorite.Slab", (Object)true);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSculpture.Cycle.Diorite.Blocks", (Object)new String[]{"DIORITE", "POLISHED_DIORITE"});
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new EarthSculptureListener(), (Plugin)ProjectKorra.plugin);
        ProjectKorra.plugin.getLogger().info("Successfully enabled " + this.getName() + " " + this.getVersion() + " by " + this.getAuthor());
    }

    @Override
    public void stop() {
    }

    @Override
    public String getAuthor() {
        return "Seabarrel";
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public String getDescription() {
        return "Sculpt earth blocks with the power of earth bending. Cycle through the different types of stone, perfect for the final detail on your home (permanently)!";
    }

    @Override
    public String getInstructions() {
        return "Tap shift on a block (example: stone), to change it's state.";
    }
}

