package com.thelastblockbender.soundwave;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class SoundwaveListener implements Listener {
  @EventHandler
  public void onSneak(final PlayerToggleSneakEvent event) {
    if (event.isCancelled() || !event.isSneaking()) {
      return;
    }
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("Soundwave")) {
      new Soundwave(event.getPlayer());
    }
  }
}
