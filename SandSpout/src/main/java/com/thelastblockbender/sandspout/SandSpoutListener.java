package com.thelastblockbender.sandspout;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class SandSpoutListener implements Listener {
  @EventHandler
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    }
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("SandSpout")) {
      new SandSpout(event.getPlayer());
    }
  }
}
