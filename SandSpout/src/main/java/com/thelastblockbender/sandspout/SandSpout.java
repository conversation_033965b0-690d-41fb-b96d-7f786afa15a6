package com.thelastblockbender.sandspout;

import java.util.EnumSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.earthbending.passive.DensityShift;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class SandSpout extends SandAbility implements AddonAbility {
  private static final Set<Material> SAND = EnumSet.of(Material.SAND, Material.SANDSTONE,
          Material.SANDSTONE_SLAB, Material.SANDSTONE_STAIRS, Material.SANDSTONE_WALL,
          Material.SMOOTH_SANDSTONE, Material.CUT_SANDSTONE, Material.CHISELED_SANDSTONE);

  private static final Set<Material> RED_SAND = EnumSet.of(Material.RED_SAND, Material.RED_SANDSTONE,
          Material.RED_SANDSTONE_SLAB, Material.RED_SANDSTONE_STAIRS, Material.RED_SANDSTONE_WALL,
          Material.SMOOTH_RED_SANDSTONE, Material.CUT_RED_SANDSTONE, Material.CHISELED_RED_SANDSTONE);

  private boolean canSpiral;
  private int angle;
  private int blindnessTime;
  private long time;
  private long interval;
  private double damage;
  @Attribute(Attribute.DURATION)
  private long duration;
  @Attribute(Attribute.COOLDOWN)
  private long cooldown;
  @Attribute(Attribute.HEIGHT)
  private double height;
  private double currentHeight;

  public SandSpout(Player player) {
    super(player);
    SandSpout oldSandSpout = getAbility(player, SandSpout.class);
    if (oldSandSpout != null) {
      oldSandSpout.remove();
      return;
    }
    if (!bPlayer.canBend(this)) {
      return;
    }
    Block topBlock = GeneralMethods.getTopBlock(player.getLocation(), 0, -50);
    if (topBlock == null) {
      topBlock = player.getLocation().getBlock();
    }
    if (!validBlock(topBlock)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    currentHeight = 0.0D;
    angle = 0;
    interval = TLBMethods.getLong("Abilities.Earth.SandSpout.Interval", currentLevel);
    canSpiral = true;
    height = TLBMethods.getDouble("Abilities.Earth.SandSpout.Height", currentLevel);
    blindnessTime = TLBMethods.getInt("Abilities.Earth.SandSpout.BlindnessTime", currentLevel);
    damage = TLBMethods.getInt("Abilities.Earth.SandSpout.SpoutDamage", currentLevel);
    duration = TLBMethods.getInt("Abilities.Earth.SandSpout.Duration", currentLevel);
    cooldown = TLBMethods.getLong("Abilities.Earth.SandSpout.Cooldown", currentLevel);
    start();
  }

  private boolean validBlock(Block block) {
    if (block == null) {
      return false;
    }
    if (!SAND.contains(block.getType()) && !RED_SAND.contains(block.getType())) {
      return false;
    }
    if (DensityShift.isPassiveSand(block)) {
      return false;
    }
    return !GeneralMethods.isRegionProtectedFromBuild(this, player.getLocation());
  }

  public void progress() {
    if (!bPlayer.canBendIgnoreBinds(this) || System.currentTimeMillis() > getStartTime() + duration) {
      remove();
      return;
    }
    Block eyeBlock = player.getEyeLocation().getBlock();
    if (eyeBlock.isLiquid() || GeneralMethods.isSolid(eyeBlock)) {
      remove();
      return;
    }

    Block block = getGround();
    if (!validBlock(block)) {
      remove();
      return;
    }

    player.setFallDistance(0);
    player.setSprinting(false);
    if (ThreadLocalRandom.current().nextInt(2) == 0) {
      playSandbendingSound(player.getLocation());
    }

    double dy = player.getLocation().getY() - block.getY();
    if (dy > height) {
      removeFlight();
    } else {
      allowFlight();
    }
    rotateSandColumn(block);
  }

  private void allowFlight() {
    player.setAllowFlight(true);
    player.setFlying(true);
    player.setFlySpeed(0.05F);
  }

  private void removeFlight() {
    player.setAllowFlight(false);
    player.setFlying(false);
  }

  private Block getGround() {
    Block standingblock = player.getLocation().getBlock();
    for (int i = 0; i <= height + 5; i++) {
      Block block = standingblock.getRelative(BlockFace.DOWN, i);
      if (GeneralMethods.isSolid(block) || block.isLiquid()) {
        return block;
      }
    }
    return null;
  }

  private void rotateSandColumn(Block block) {
    Location location = block.getLocation();
    Location playerLoc = player.getLocation();
    location = new Location(location.getWorld(), playerLoc.getX(), location.getY(), playerLoc.getZ());

    double dy = playerLoc.getY() - block.getY();
    if (dy > height) {
      dy = height;
    }

    int index = angle;

    angle += 1;
    if (angle >= 8) {
      angle = 0;
    }
    for (int i = 1; i <= dy; i++) {
      index++;
      if (index >= 8) {
        index = 0;
      }
      Location center = new Location(location.getWorld(), location.getX(), block.getY() + i, location.getZ());
      boolean redSand = RED_SAND.contains(block.getType());
      displaySandParticle(center, 1, 1, 1, 1, 0.2, redSand);
      if (canSpiral) {
        displayHelix(block, redSand);
      }
      for (final Entity e : GeneralMethods.getEntitiesAroundPoint(center, 1.5)) {
        if (e instanceof LivingEntity) {
          final LivingEntity entity = (LivingEntity) e;
          if (e.getEntityId() != player.getEntityId()) {
            if (!entity.hasPotionEffect(PotionEffectType.BLINDNESS)) {
              entity.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, blindnessTime * 20, 1));
            }
          }
        }
      }
    }
  }

  private void displayHelix(Block block, boolean redSand) {
    Location loc = block.getLocation();
    currentHeight += 0.1D;
    if (currentHeight >= player.getLocation().getY() - loc.getY()) {
      currentHeight = 0.0D;
    }
    for (int points = 0; points <= 2; points++) {
      double x = Math.cos(currentHeight);
      double z = Math.sin(currentHeight);
      double nx = (x * -0.5) - 0.55;
      double nz = (z * -0.5) - 0.55;
      Location newLoc = new Location(player.getWorld(), loc.getX() + x, loc.getY() + currentHeight, loc.getZ() + z);
      Location secondLoc = new Location(player.getWorld(), loc.getX() + nx, loc.getY() + currentHeight, loc.getZ() + nz);
      displaySandParticle(newLoc.add(0.75D, 0.75D, 0.75D), 1, 0.1, 0.1, 0.1, 1.0, redSand);
      displaySandParticle(secondLoc.add(0.75D, 0.75D, 0.75D), 1, 0.1, 0.1, 0.1, 1.0, redSand);
    }
  }

  public boolean removeSpouts(Location location, double radius, Player sourcePlayer) {
    boolean removed = false;
    for (SandSpout spout : getAbilities(SandSpout.class)) {
      if (!player.equals(sourcePlayer)) {
        Location loc1 = player.getLocation().getBlock().getLocation();
        location = location.getBlock().getLocation();
        double dx = loc1.getX() - location.getX();
        double dy = loc1.getY() - location.getY();
        double dz = loc1.getZ() - location.getZ();

        double distance = Math.sqrt(dx * dx + dz * dz);

        if ((distance <= radius) && (dy > 0.0D) && (dy < height)) {
          spout.remove();
          removed = true;
        }
      }
    }
    return removed;
  }

  public void remove() {
    super.remove();
    removeFlight();
    bPlayer.addCooldown(this);
  }

  public String getName() {
    return "SandSpout";
  }

  public Location getLocation() {
    return player != null ? player.getLocation() : null;
  }

  public long getCooldown() {
    return this.cooldown;
  }

  public boolean isSneakAbility() {
    return true;
  }

  public boolean isHarmlessAbility() {
    return false;
  }

  public boolean isCanSpiral() {
    return canSpiral;
  }

  public void setCanSpiral(boolean canSpiral) {
    this.canSpiral = canSpiral;
  }

  public int getAngle() {
    return angle;
  }

  public void setAngle(int angle) {
    this.angle = angle;
  }

  public int getBlindnessTime() {
    return blindnessTime;
  }

  public void setBlindnessTime(int blindnessTime) {
    this.blindnessTime = blindnessTime;
  }

  public long getTime() {
    return time;
  }

  public void setTime(long time) {
    this.time = time;
  }

  public long getInterval() {
    return interval;
  }

  public void setInterval(long interval) {
    this.interval = interval;
  }

  public double getDamage() {
    return damage;
  }

  public void setDamage(double damage) {
    this.damage = damage;
  }

  public double getHeight() {
    return height;
  }

  public void setHeight(double height) {
    this.height = height;
  }

  public double getCurrentHeight() {
    return currentHeight;
  }

  public void setCurrentHeight(double currentHeight) {
    this.currentHeight = currentHeight;
  }

  @Override
  public String getAuthor() {
    return "Nick";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SandSpoutListener(), ProjectKorra.plugin);

    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.Interval", 1);
    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.Height", 20);
    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.BlindnessTime", 20);
    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.SpoutDamage", 1);
    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.Duration", 3000);
    ConfigManager.getConfig().addDefault("Abilities.Earth.SandSpout.Cooldown", 2000);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
  }
}
