package com.thelastblockbender.leafdome;

import java.util.HashSet;
import java.util.Set;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;

public class LeafDome extends PlantAbility implements AddonAbility {
  private final Set<TempBlock> domeShield = new HashSet<>();
  private int radius;
  private long cooldown;
  private long duration;

  public LeafDome(Player player) {
    super(player);
    if (!bPlayer.canBend(this) || hasAbility(player, LeafDome.class)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.duration = TLBMethods.getInt("Abilities.Plantbending.LeafDome.Duration", currentLevel);
    this.cooldown = TLBMethods.getInt("Abilities.Plantbending.LeafDome.Cooldown", currentLevel);
    this.radius = TLBMethods.getInt("Abilities.Plantbending.LeafDome.Radius", currentLevel);

    start();
  }

  @Override
  public void progress() {
    if (!bPlayer.canBend(this) || !player.isSneaking()) {
      remove();
      return;
    }
    if (System.currentTimeMillis() > this.getStartTime() + this.duration) {
      remove();
      return;
    }
    Block block = getGround();
    if (!isGroundPlant(block)) {
      remove();
      return;
    }
    revertLeaves();
    for (Location loc : GeneralMethods.getCircle(player.getLocation(), radius, 0, true, true, 0)) {
      if (loc.getBlock().isPassable() && !TempBlock.isTempBlock(loc.getBlock())) {
        domeShield.add(new TempBlock(loc.getBlock(), Material.OAK_LEAVES));
      }
    }
    for (Location loc : GeneralMethods.getCircle(player.getLocation(), radius - 1, 0, false, true, 0)) {
      if (loc.getBlock().getType() == Material.WATER && !TempBlock.isTempBlock(loc.getBlock())) {
        domeShield.add(new TempBlock(loc.getBlock(), Material.AIR));
      }
    }
  }

  private Block getGround() {
    Block standingblock = player.getLocation().getBlock();
    for (int i = 0; i <= Math.max(radius, 5); i++) {
      Block block = standingblock.getRelative(BlockFace.DOWN, i);
      if (GeneralMethods.isSolid(block) || block.isLiquid()) {
        return block;
      }
    }
    return null;
  }

  private void revertLeaves() {
    for (TempBlock tb : domeShield) {
      if (tb.getBlock().getType() == Material.OAK_LEAVES) {
        tb.revertBlock();
      }
    }
    domeShield.clear();
  }

  @Override
  public void remove() {
    super.remove();
    if (!domeShield.isEmpty()) {
      bPlayer.addCooldown(this);
      revertLeaves();
    }
  }

  @Override
  public long getCooldown() {
    return this.cooldown;
  }

  @Override
  public Location getLocation() {
    return null;
  }

  @Override
  public String getName() {
    return "LeafDome";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public String getInstructions() {
    return "Hold shift to form a protective dome made of roots, vines and other plants." +
      "Make sure you are standing on a soft surface so you can pull roots and plants from the ground." +
      "(Sand, Dirt, Grass, etc).";
  }

  @Override
  public String getDescription() {
    return "Manipulate plants to surround yourself in a dome of protective plants!";
  }

  @Override
  public boolean isEnabled() {
    return true;
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new LeafDomeListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafDome.Cooldown", 3000);
    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafDome.Radius", 2);
    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafDome.Duration", 1000);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
    super.remove();
  }
}
