package me.numin.summonself;

import java.util.HashMap;
import java.util.UUID;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class SummonSelfHelper implements Listener {
	
	static HashMap<UUID, Integer> currentBlasts = new HashMap<UUID, Integer>();
	static HashMap<UUID, Long> startTime = new HashMap<UUID, Long>();

}
