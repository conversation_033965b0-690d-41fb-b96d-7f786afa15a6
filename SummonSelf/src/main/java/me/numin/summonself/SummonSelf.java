// 
// Decompiled by Procyon v0.5.36
// 

package me.numin.summonself;

import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import org.bukkit.World;
import org.bukkit.Bukkit;
import org.bukkit.Color;

import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.firebending.FireShield;
import com.projectkorra.projectkorra.airbending.AirShield;
import java.util.Iterator;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;

import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import org.bukkit.Particle;
import org.bukkit.block.Block;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.bukkit.Location;
import java.util.ArrayList;
import org.bukkit.entity.ArmorStand;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.FlightAbility;

public class SummonSelf extends FlightAbility implements AddonAbility, ComboAbility
{
    private ArmorStand armorStand;
    private ArrayList<Integer> armorStands;
    private Location location;
    private Location origin;
    private Vector direction;
    private boolean standIsCreated;
    private boolean pushUser;
    private double damage;
    private double radius;
    private double range;
    private double speed;
    private double targetLift;
    private double targetPush;
    private double userLift;
    private double userPush;
    private double duration;
    private long cooldown;
    private long time;
    private double totalBlasts;
    private double currentBlasts;
    private long currentLevel;
    
    public SummonSelf(final Player player) {
        super(player);
        this.armorStands = new ArrayList<Integer>();
        if (!this.bPlayer.canBendIgnoreBinds((CoreAbility)this)) {
            return;
        }
        final int statLevel = StatisticsMethods.getId("AbilityLevel_SummonSelf");
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);
        this.origin = player.getLocation().add(0.0, 1.0, 0.0);
        this.location = this.origin.clone();
        this.direction = this.location.getDirection().clone().normalize();
        this.pushUser = true;
        this.cooldown = TLBMethods.getLong("ExtraAbilities.SummonSelf.Cooldown", this.currentLevel);
        this.radius = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Radius", this.currentLevel);
        this.range = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Range", this.currentLevel);
        this.speed = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Speed", this.currentLevel);
        this.damage = ConfigManager.getConfig().getDouble("ExtraAbilities.SummonSelf.Damage");
        this.targetLift = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Velocities.Target.Lift", this.currentLevel);
        this.targetPush = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Velocities.Target.Push", this.currentLevel);
        this.userLift = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Velocities.User.Lift", this.currentLevel);
        this.userPush = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Velocities.User.Push", this.currentLevel);
        this.duration = TLBMethods.getDouble("ExtraAbilities.SummonSelf.Duration", this.currentLevel);
        this.totalBlasts = TLBMethods.getDouble("ExtraAbilities.SummonSelf.TotalBlasts", this.currentLevel);
        if (player.isSprinting()) {
            if (SummonSelfHelper.currentBlasts.get(player.getUniqueId()) != null) {
                SummonSelfHelper.currentBlasts.put(player.getUniqueId(), SummonSelfHelper.currentBlasts.get(player.getUniqueId()) + 1);
            }
            else {
                SummonSelfHelper.currentBlasts.put(player.getUniqueId(), 1);
            }
            if (SummonSelfHelper.startTime.get(player.getUniqueId()) == null) {
                SummonSelfHelper.startTime.put(player.getUniqueId(), System.currentTimeMillis());
            }
            this.start();
            this.time = System.currentTimeMillis();
        }
    }
    
    public void progress() {
        if (this.player.isDead() || !this.player.isOnline() || GeneralMethods.isRegionProtectedFromBuild((Ability)this, this.getLocation())) {
            SummonSelfHelper.currentBlasts.put(this.player.getUniqueId(), 0);
            SummonSelfHelper.startTime.remove(this.player.getUniqueId());
            this.remove();
            return;
        }
        if (SummonSelfHelper.currentBlasts.get(this.player.getUniqueId()) > this.totalBlasts) {
            SummonSelfHelper.currentBlasts.put(this.player.getUniqueId(), 0);
            SummonSelfHelper.startTime.remove(this.player.getUniqueId());
            this.bPlayer.addCooldown((Ability)this);
            this.remove();
            return;
        }
        if (System.currentTimeMillis() > SummonSelfHelper.startTime.get(this.player.getUniqueId()) + this.duration) {
            SummonSelfHelper.currentBlasts.put(this.player.getUniqueId(), 0);
            SummonSelfHelper.startTime.remove(this.player.getUniqueId());
            this.bPlayer.addCooldown((Ability)this);
            this.remove();
            return;
        }
        if (this.origin.distance(this.getLocation()) <= this.range) {
            if (!this.standIsCreated) {
                this.armorStand = this.getArmorStand();
            }
            for (final Block block : GeneralMethods.getBlocksAroundPoint(this.armorStand.getLocation(), this.radius - 1.0)) {
                if (block.isLiquid() || GeneralMethods.isSolid(block)) {
                    this.armorStand.getWorld().spawnParticle(Particle.CLOUD, this.armorStand.getLocation().add(0.0, 1.0, 0.0), 20, 0.1, 0.1, 0.1, 0.2);
                }
            }
            for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(this.armorStand.getLocation(), this.radius)) {
                if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId() && entity.getUniqueId() != this.armorStand.getUniqueId()) {
                    final Vector vector = this.armorStand.getLocation().getDirection().normalize();
                    if (this.targetLift != 0.0) {
                        vector.setY(this.targetLift);
                    }
                    entity.getWorld().spawnParticle(Particle.CLOUD, this.armorStand.getLocation().add(0.0, 1.0, 0.0), 20, 0.1, 0.1, 0.1, 0.2);
                    entity.setVelocity(vector.multiply(this.targetPush));
                    DamageHandler.damageEntity(entity, this.damage, (Ability)this);
                    this.destroyArmorStands();
                    this.remove();
                    this.bPlayer.addCooldown((Ability)this);
                    SummonSelfHelper.startTime.remove(this.player.getUniqueId());
                    SummonSelfHelper.currentBlasts.put(this.player.getUniqueId(), 0);
                    return;
                }
            }
            this.checkCollisions();
            this.moveArmorStand();
        }
    }
    
    private void checkCollisions() {
        final CoreAbility airShield = CoreAbility.getAbility((Class)AirShield.class);
        final CoreAbility fireShield = CoreAbility.getAbility((Class)FireShield.class);
        final CoreAbility summonSelf = CoreAbility.getAbility((Class)SummonSelf.class);
        final CoreAbility[] abilities = { airShield, fireShield };
        CoreAbility[] array;
        for (int length = (array = abilities).length, i = 0; i < length; ++i) {
            final CoreAbility ability = array[i];
            ProjectKorra.getCollisionManager().addCollision(new Collision(ability, summonSelf, false, true));
        }
    }
    
    private void destroyArmorStands() {
        for (final World world : Bukkit.getServer().getWorlds()) {
            for (final Entity entity : world.getEntities()) {
                if (this.armorStands.contains(entity.getEntityId())) {
                    entity.remove();
                }
            }
        }
    }
    
    private ArmorStand getArmorStand() {
        final ArmorStand stand = (ArmorStand)this.player.getWorld().spawn(this.origin, (Class)ArmorStand.class);
        stand.setCanPickupItems(false);
        stand.setGravity(true);
        stand.setVisible(false);
        stand.setMarker(false);
        stand.setCollidable(true);
        this.armorStands.add(stand.getEntityId());
        this.standIsCreated = true;
        return stand;
    }
    
    private void moveArmorStand() {
        if (this.pushUser) {
            final Vector vector = this.player.getLocation().getDirection().normalize();
            if (this.userLift != 0.0) {
                vector.setY(this.userLift);
            }
            this.player.setVelocity(vector.multiply(-this.userPush));
            this.pushUser = false;
        }
        final Location newStandLoc = this.location.add(this.direction.multiply(this.speed));
        this.armorStand.teleport(newStandLoc);

        ParticleEffect.SPELL_MOB.display(this.armorStand.getLocation().add(0.0, 0.4, 0.0), 10, 0.07, 0.9, 0.07, 0.0, Color.fromRGB(255, 255, 255));
        ParticleEffect.SPELL_MOB.display(this.armorStand.getLocation().add(0.0, 0.5, 0.0), 10, 0.7, 0.1, 0.7, 0.0, Color.fromRGB(255, 255, 255));
        ParticleEffect.SPELL_MOB.display(this.armorStand.getLocation().add(0.0, 2.0, 0.0), 10, 0.3, 0.3, 0.3, 0.0, Color.fromRGB(255, 255, 255));
        ParticleEffect.CLOUD.display(this.armorStand.getLocation().add(0.0, 1.0, 0.0), 1, 1.0, 1.0, 1.0, 0.0);
    }

    public void remove() {
        this.destroyArmorStands();
        super.remove();
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public double getCollisionRadius() {
        return this.radius;
    }
    
    public String getName() {
        return "SummonSelf";
    }
    
    public Location getLocation() {
        return this.armorStands.isEmpty() ? this.player.getLocation() : this.armorStand.getLocation();
    }
    
    public void load() {
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Cooldown", (Object)10000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Range", (Object)15);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Radius", (Object)2);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Speed", (Object)1);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Damage", (Object)3);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Velocities.Target.Lift", (Object)0.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Velocities.Target.Push", (Object)1.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Velocities.User.Lift", (Object)0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.SummonSelf.Velocities.User.Push", (Object)2);
        ConfigManager.defaultConfig.save();
        ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility)this);
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }
    
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
    
    public String getAuthor() {
        return "Numin";
    }
    
    public String getVersion() {
        return "2.0";
    }
    
    public Object createNewComboInstance(final Player player) {
        return new SummonSelf(player);
    }
    
    public ArrayList<ComboManager.AbilityInformation> getCombination() {
        final ArrayList<ComboManager.AbilityInformation> combo = new ArrayList<ComboManager.AbilityInformation>();
        combo.add(new ComboManager.AbilityInformation("AirBlast", ClickType.SHIFT_DOWN));
        combo.add(new ComboManager.AbilityInformation("AirBlast", ClickType.SHIFT_UP));
        combo.add(new ComboManager.AbilityInformation("AirBlast", ClickType.SHIFT_DOWN));
        combo.add(new ComboManager.AbilityInformation("AirBlast", ClickType.SHIFT_UP));
        combo.add(new ComboManager.AbilityInformation("AirBurst", ClickType.LEFT_CLICK));
        return combo;
    }
    
    public String getDescription() {
        return "While in a powerful rush, you're able to take your momentum and force it outward in your direction! Launch a clone-like current of yourself towards opponents to deal knockback and damage.";
    }
    
    public String getInstructions() {
        return "AirBlast (Tap shift 2x) > AirBurst (Sprint + Left-click)";
    }
}
