package com.chrismwiggs.VineGrapple;

import java.util.List;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.chrismwiggs.RootSpear.RootSpear;
import com.chrismwiggs.RootSpear.RootSpearListener;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import net.md_5.bungee.api.ChatColor;

public class VineGrapple extends PlantAbility implements AddonAbility {

	// grapple variables
	private Location target;
	private int gRange, gMax;
	private boolean pulling;
	private double gSpeed;
	private long cooldown;
	private World origin;
	
	// general variables
	private Location current;
	private Vector direction;
	long currentLevel;
			
	public VineGrapple(Player player) {
		super(player);
		

		if (bPlayer.isOnCooldown(this)) {
			return;
		}
		
		if (!bPlayer.canBend(this)) {
			return;
		}
		
		
	
		this.origin = player.getWorld();
		this.gMax = ConfigManager.getConfig().getInt("Abilities.Plantbending.VineGrapple.Range");
		this.gSpeed = ConfigManager.getConfig().getDouble("Abilities.Plantbending.VineGrapple.Speed");
		this.pulling = false;
		this.gRange = 0;
		this.cooldown = ConfigManager.getConfig().getLong("Abilities.Plantbending.VineGrapple.Cooldown");
		
		
		
		if (!player.getWorld().equals(origin)) {
			remove();
			return;
		}
		
		//modify();
		start();
	}

	private void modify() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		
		currentLevel = GeneralMethods.limitLevels(player, statLevel);
		
		
		this.gMax = (int) ((currentLevel * 1.5) + 5);
		
	}

	
	private void reset() {
		
		this.target = null;
		this.pulling = false;
		this.gRange = 0;
	}
	
	@Override
	public void progress() {
		
		
		this.current = GeneralMethods.getRightSide(player.getLocation(), 0.45).add(0, 1, 0);
		this.target = player.getTargetBlock(getTransparentMaterialSet(), gMax).getLocation().clone().add(0.5, 0.5, 0.5);

		
		if (current.distance(target) < 2) {
			this.reset();
			return;
		}
		
		if (!pulling) {
			gRange++;
		
			if (gRange >= gMax) {
				this.reset();
				return;
			}
		} else {
			gRange = (int) Math.floor(current.distance(target));
		}
		
		Vector direction = GeneralMethods.getDirection(current, target).normalize();
		
		for (int i = 0; i < gRange; i++) {
			current.add(direction);
			
			GeneralMethods.displayColoredParticle("3D9970", current);
			
			if (!current.getBlock().isPassable() && !pulling) {
				if (current.distance(target) < 1) {
					this.pulling = true;
				} else {
					this.reset();
					return;
				}
			}
		}
		
		if (pulling) {
			player.setVelocity(direction.multiply(gSpeed));
		}
	}

	
	@Override
	public String getDescription() {
		return "Manipulate plants into a strong vine and direct them at a point to grapple!";
	}
	
	public String getInstructions() {
		return  "Simply aim and click to throw your vine and be pulled towards a point.";
	}



	@Override
	public String getName() {
		return "VineGrapple";
	}


	@Override
	public boolean isHarmlessAbility() {
		// TODO Auto-generated method stub
		return false;
	}


	@Override
	public boolean isSneakAbility() {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public String getAuthor() {
		return "Simplicitee, modified by CMW";
	}

	@Override
	public long getCooldown() {
		return this.cooldown;
	}
	
	@Override
	public String getVersion() {
		return "1.0.0";
	}
	


	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new VineGrappleListener(), ProjectKorra.plugin);
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");
		
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Cooldown", 3000);
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Damage", 2);
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Range", 20);
		ConfigManager.defaultConfig.save();
		
	}
	
	@Override
	public Location getLocation() {
		return player.getLocation();
	}

	@Override
	public boolean isEnabled() {
		return true;
	}


	@Override
	public void stop() {
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
		super.remove();
		
	}

	
}
