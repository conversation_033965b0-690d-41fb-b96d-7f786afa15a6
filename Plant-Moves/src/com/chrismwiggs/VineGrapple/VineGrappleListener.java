package com.chrismwiggs.VineGrapple;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;

import com.chrismwiggs.Tangle.Tangle;
import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.util.MovementHandler;
import com.projectkorra.projectkorra.waterbending.blood.Bloodbending;

public class VineGrappleListener implements Listener {

	@EventHandler
	public void onSwing(PlayerAnimationEvent event) {
		if (event.isCancelled()) {
			return;
			
		} else if (CoreAbility.hasAbility(event.getPlayer(), VineGrapple.class)) {
			return;
			
		}
		new VineGrapple(event.getPlayer());
	
		
	
	}
}
