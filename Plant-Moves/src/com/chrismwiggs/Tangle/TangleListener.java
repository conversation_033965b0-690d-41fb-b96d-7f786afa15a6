package com.chrismwiggs.Tangle;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;


public class <PERSON><PERSON><PERSON>ist<PERSON> implements Listener {


	public void onLeftClick(final PlayerInteractEvent event) {
		if (event.getHand() != EquipmentSlot.HAND) {
			return;
		}
		if (event.getAction() != Action.LEFT_CLICK_BLOCK && event.getAction() != Action.LEFT_CLICK_AIR) {
			return;
		}
		if (event.getAction() == Action.LEFT_CLICK_BLOCK && event.isCancelled()) {
			return;
		}
		
		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		
		if (bPlayer == null) {
			return;
		}
		
		CoreAbility ability = bPlayer.getBoundAbility();
		
		if (canBend(player, "Tangle")) {
			new Tangle(player);
		}
		
	}
	
	private boolean canBend(Player player, String ability) {
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
		CoreAbility abil = CoreAbility.getAbility(ability);
		
		if (abil == null) {
			return false;
		} else if (!bPlayer.getBoundAbilityName().equals(ability)) {
			return false;
		} else if (!bPlayer.canBend(abil)) {
			return false;
		}
		
		return true;
	}
	
}
