package com.chrismwiggs.Tangle;

import java.util.List;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.earthbending.EarthArmor;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.MovementHandler;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import net.md_5.bungee.api.ChatColor;

public class Tangle extends PlantAbility implements AddonAbility {

	
	private double tRadius;
	private long tDuration;
	private double tRange;
	private int angle;
	private long cooldown;
		
	// general variables
	private Location current;
	private Vector direction;
	long currentLevel;
	
	public Tangle(Player player) {
		super(player);
		
		if (bPlayer.isOnCooldown(this)) {
			return;
		} else if (hasAbility(player, EarthArmor.class)) {
			return;
		}
		
		if (!bPlayer.canBend(this)) {
			return;
		}
		
		if (hasAbility(player, Tangle.class)) {
			Tangle abil = getAbility(player, Tangle.class);
			return;
		}
		
		
		this.current = null;
		this.tRadius = ConfigManager.getConfig().getDouble("Abilities.Plantbending.Tangle.Radius");
		this.tRange = ConfigManager.getConfig().getDouble("Abilities.Plantbending.Tangle.Range");
		this.tDuration = ConfigManager.getConfig().getLong("Abilities.Plantbending.Tangle.Duration");
		this.angle = 0;
		this.cooldown = ConfigManager.getConfig().getLong("Abilities.Plantbending.Tangle.Cooldown");
		
		modify();
		start();
	}
	
	private void modify() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		
		currentLevel = GeneralMethods.limitLevels(player, statLevel);
		
		
		this.tRange = (int) ((currentLevel * 1.5) + 5);
		
	}

	@Override
	public void progress() {
		if (!player.isOnline() || player.isDead()) {
			remove();
			return;
		}
		
		if (GeneralMethods.isWeapon(player.getInventory().getItemInMainHand().getType())) return;
		
		if (player.getEyeLocation().distance(player.getEyeLocation()) > tRange) {
			this.reset();
			return;
		}
		
		player.getEyeLocation().add(player.getEyeLocation().getDirection().clone().normalize());
		
		if (!player.getEyeLocation().getBlock().isPassable()) {
			this.reset();
			return;
		}
		
		for (Entity e : GeneralMethods.getEntitiesAroundPoint(player.getEyeLocation(), tRadius + 0.5)) {
			if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId()) {
				new MovementHandler((LivingEntity) e, this).stopWithDuration(tDuration / 1000 * 20, ChatColor.DARK_AQUA + "* Tangled *");
				new TempBlock(e.getLocation().getBlock(), Material.OAK_LEAVES).setRevertTime(tDuration);
				new TempBlock(e.getLocation().clone().add(0, e.getHeight(), 0).getBlock(), Material.OAK_LEAVES).setRevertTime(tDuration);
				
				this.reset();
				
				return;
			}
		}
		
		for (int i = 0; i < 3; i++) {
			Vector ov = GeneralMethods.getOrthogonalVector(player.getEyeLocation().getDirection().clone().normalize(), (double) (angle + (120 * i)), tRadius);
			Location pl = player.getEyeLocation().clone().add(ov.clone());
			GeneralMethods.displayColoredParticle("3D9970", pl);
		}
		
		angle += 30;
		
		if (angle == 360) {
			angle = 0;
		}
	}
	
	
	private void reset() {
		bPlayer.addCooldown(this);
		this.current = null;
		this.direction = null;
		this.angle = 0;
	}

	@Override
	public long getCooldown() {
		return this.cooldown;
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public String getName() {
		return "Tangle";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return false;
	}

	@Override
	public String getAuthor() {
		return "Chris M Wiggs, based on Simplicitee's codebase";
	}

	@Override
	public String getVersion() {
		return "1.0.0";
	}
	
	@Override
	public String getInstructions() {
		return  "Left click to send a root lashing at your opponent! \n"
				+ "Make sure you are standing on a soft surface so you can pull roots from the ground."
				+ "(Sand, Dirt, Grass, etc).";
	}
	
	@Override
	public String getDescription() {
		return  "Pull roots out of the ground and send them hurling towards your foes!";
	}

	@Override
	public boolean isEnabled() {
		return true;
	}
	
	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new TangleListener(), ProjectKorra.plugin);
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");
		
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Cooldown", 3000);
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Damage", 2);
		ConfigManager.getConfig().addDefault("Abilities.Plantbending.VineWhip.Range", 20);
		ConfigManager.defaultConfig.save();
	}

	@Override
	public void stop() {
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
		super.remove();
	}
	
}
