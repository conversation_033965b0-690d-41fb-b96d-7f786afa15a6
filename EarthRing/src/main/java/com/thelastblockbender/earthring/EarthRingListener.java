package me.simplicitee;

import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class EarthRingListener implements Listener {
   @EventHandler
   public void onSwing(PlayerAnimationEvent event) {
      if (!event.isCancelled()) {
         if (CoreAbility.hasAbility(event.getPlayer(), EarthRing.class)) {
            EarthRing abil = (EarthRing)CoreAbility.getAbility(event.getPlayer(), EarthRing.class);
            abil.setSpinning(false);
         }

      }
   }
}
