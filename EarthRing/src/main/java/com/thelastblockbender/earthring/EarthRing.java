package me.simplicitee;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.configuration.Config;
import com.projectkorra.projectkorra.earthbending.EarthArmor;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import java.io.File;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.permissions.Permission;
import org.bukkit.permissions.PermissionDefault;
import org.bukkit.util.Vector;

public class EarthRing extends EarthAbility implements AddonAbility, ComboAbility {
   public static Config config;
   private int hitpoints;
   private int grabRadius;
   private double damage;
   private double spDmg;
   private double speed;
   private double spin;
   private int maxRange;
   private int range = 0;
   private double angle = 0.0D;
   private int radius;
   private Material m;
   private TempBlock tb;
   private boolean spinning = true;
   private Location center;
   public Block source;
   public Material sourceType;
   private TempBlock spinningBlock;
   private long currentLevel;

   public EarthRing(Player player) {
      super(player);
      if (this.bPlayer.canBendIgnoreBinds(this) && isEarthbendable(player.getLocation().getBlock().getRelative(BlockFace.DOWN).getType(), true, true, false)) {
         this.setFields();
         this.source = getRandomEarthBlock(player, player.getLocation(), this.grabRadius, true, true, true);
         this.sourceType = this.source.getType();
         if (this.source == null) {
            this.remove();
         } else {
            this.m = this.source.getType();
            this.tb = new TempBlock(this.source, Material.AIR);
            this.modify();
            this.start();
         }
      }
   }

   private void modify() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = TLBMethods.limitLevels(this.player, statLevel);
      this.maxRange = (int)(this.currentLevel + 8L);
      this.radius = (int)((double)this.currentLevel * 0.2D + 1.0D);
      this.grabRadius = (int)((double)this.currentLevel * 0.3D + 2.0D);
   }

   public void setFields() {
      this.hitpoints = config.get().getInt("Abilities.EarthRing.HitPoints");
      this.grabRadius = config.get().getInt("Abilities.EarthRing.GrabRadius");
      this.damage = config.get().getDouble("Abilities.EarthRing.ShotDamage");
      this.spDmg = config.get().getDouble("Abilities.EarthRing.SpinDamage");
      this.speed = config.get().getDouble("Abilities.EarthRing.ShootSpeed");
      this.spin = config.get().getDouble("Abilities.EarthRing.SpinSpeed");
      this.maxRange = config.get().getInt("Abilities.EarthRing.MaxRange");
      this.radius = config.get().getInt("Abilities.EarthRing.Radius");
   }

   public long getCooldown() {
      return config.get().getLong("Abilities.EarthRing.Cooldown");
   }

   public Location getLocation() {
      return this.center;
   }

   public String getName() {
      return "EarthRing";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public void progress() {
      if (this.player == null) {
         this.remove();
      } else if (this.player.isOnline() && !this.player.isDead()) {
         if (this.spinning) {
            this.center = this.player.getEyeLocation().clone();
            double x = Math.cos(this.angle);
            double z = Math.sin(this.angle);
            this.center.add(x * (double)this.radius, 0.0D, z * (double)this.radius);
            this.displayParticles();
            this.angle += 3.141592653589793D / this.spin;
            Iterator var6 = GeneralMethods.getEntitiesAroundPoint(this.center, 2.0D).iterator();

            while(var6.hasNext()) {
               Entity e = (Entity)var6.next();
               if (e instanceof LivingEntity && e.getEntityId() != this.player.getEntityId()) {
                  if (EarthAbility.isMetalBlock(this.source)) {
                     DamageHandler.damageEntity(e, this.player, this.spDmg * 1.2D, this);
                  } else {
                     DamageHandler.damageEntity(e, this.player, this.spDmg, this);
                  }

                  --this.hitpoints;
                  this.remove();
                  return;
               }
            }

            if (this.hitpoints <= 0) {
               this.remove();
            }
         } else {
            Vector direction = this.player.getLocation().getDirection().clone();
            this.center = this.center.add(direction.normalize().multiply(this.speed));
            ++this.range;
            if (this.range > this.maxRange) {
               this.remove();
               return;
            }

            this.displayParticles();
            Iterator var8 = GeneralMethods.getEntitiesAroundPoint(this.center, 1.5D).iterator();

            while(var8.hasNext()) {
               Entity e2 = (Entity)var8.next();
               if (e2 instanceof LivingEntity && e2.getEntityId() != this.player.getEntityId()) {
                  if (EarthAbility.isMetalBlock(this.source)) {
                     DamageHandler.damageEntity(e2, this.player, this.damage * 1.2D, this);
                  } else {
                     DamageHandler.damageEntity(e2, this.player, this.damage, this);
                  }

                  this.remove();
                  return;
               }
            }
         }

      } else {
         this.remove();
      }
   }

   public void displayParticles() {
      Color color = Color.fromRGB(EarthArmor.getColor(this.m));
      String hex = color.toString().substring(12, color.toString().length() - 1);
      double x = Math.cos(this.angle);
      double z = Math.sin(this.angle);
      Location clone = this.center.clone().add(x, 0.0D, z);
      this.spinningBlock = new TempBlock(clone.getBlock(), this.sourceType);
      this.spinningBlock.setRevertTime(50L);
   }

   public static Block getRandomEarthBlock(Player player, Location location, int radius, boolean earth, boolean sand, boolean metal) {
      List<Integer> checked = new ArrayList();
      List<Block> blocks = GeneralMethods.getBlocksAroundPoint(location, (double)radius);

      for(int i = 0; i < blocks.size(); ++i) {
         int index;
         for(index = (new Random()).nextInt(blocks.size()); checked.contains(index); index = (new Random()).nextInt(blocks.size())) {
         }

         checked.add(index);
         Block block = (Block)blocks.get(index);
         if (block != null && block.getLocation().distance(location) >= 2.0D && isTransparent(player, block.getRelative(BlockFace.UP))) {
            if (isEarth(block) && earth) {
               return block;
            }

            if (isSand(block) && sand) {
               return block;
            }

            if (isMetal(block) && metal) {
               return block;
            }
         }
      }

      return null;
   }

   public void setSpinning(boolean spin) {
      this.spinning = spin;
   }

   public void remove() {
      super.remove();
      if (this.tb != null) {
         this.tb.setRevertTime(5000L);
         this.bPlayer.addCooldown(this);
      }

   }

   public String getAuthor() {
      return "Simp";
   }

   public String getVersion() {
      return "1.0.3";
   }

   public void load() {
      config = new Config(new File("Simplicitee.yml"));
      FileConfiguration c = config.get();
      c.addDefault("Abilities.EarthRing.Cooldown", 6000);
      c.addDefault("Abilities.EarthRing.HitPoints", 5);
      c.addDefault("Abilities.EarthRing.GrabRadius", 4);
      c.addDefault("Abilities.EarthRing.ShotDamage", 2);
      c.addDefault("Abilities.EarthRing.SpinDamage", 1);
      c.addDefault("Abilities.EarthRing.ShootSpeed", 1.7D);
      c.addDefault("Abilities.EarthRing.SpinSpeed", 8);
      c.addDefault("Abilities.EarthRing.MaxRange", 25);
      config.save();
      ProjectKorra.getCollisionInitializer().addLargeAbility(this);
      ProjectKorra.getCollisionInitializer().addRemoveSpoutAbility(this);
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new EarthRingListener(), ProjectKorra.plugin);
      if (ProjectKorra.plugin.getServer().getPluginManager().getPermission("bending.ability.earthring") == null) {
         Permission perm = new Permission("bending.ability.earthring");
         perm.setDefault(PermissionDefault.TRUE);
         ProjectKorra.plugin.getServer().getPluginManager().addPermission(perm);
      }

      ProjectKorra.log.info("Successfully enabled " + this.getName() + " " + this.getVersion() + " by " + this.getAuthor());
   }

   public void stop() {
   }

   public String getDescription() {
      return "Using advanced techniques, this ability allows the bender to create a small block of stones and earth moving around their body. This acts as a shield and can be turned offensive, and automatically sources the block it will use.";
   }

   public String getInstructions() {
      return "EarthShard (Left Click 2x) > EarthAmor (Hold Shift), click to shoot the earth in the direction you are looking or use the ring as a shield.";
   }

   public Object createNewComboInstance(Player player) {
      return new EarthRing(player);
   }

   public ArrayList<AbilityInformation> getCombination() {
      ArrayList<AbilityInformation> combo = new ArrayList();
      combo.add(new AbilityInformation("EarthShard", ClickType.LEFT_CLICK));
      combo.add(new AbilityInformation("EarthShard", ClickType.LEFT_CLICK));
      combo.add(new AbilityInformation("EarthArmor", ClickType.SHIFT_DOWN));
      return combo;
   }
}
