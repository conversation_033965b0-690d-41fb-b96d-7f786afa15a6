package com.NickC1211.korra.AirJab;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import java.util.List;
import java.util.Random;
import java.util.logging.Logger;
import org.bukkit.Location;
import org.bukkit.Server;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.plugin.PluginManager;
import org.bukkit.util.Vector;

public class AirJab extends AirAbility implements AddonAbility {
  private long cooldown;
  private double range;
  private double speed;
  private double damage;
  private double push;
  private Location location;
  private Location origin;
  private Vector direction;
  private int collisionRadius;
  private int maxBlasts;
  private int currentBlasts;
  
  public AirJab(Player player)
  {
    super(player);
    
    if (!bPlayer.canBend(this)) {
      return;
    }
    
    setFields();
    start();
  }
	  
  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.NickC1211.AirJab.Cooldown", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.NickC1211.AirJab.Range", currentLevel);
    speed = TLBMethods.getDouble("ExtraAbilities.NickC1211.AirJab.Speed", currentLevel);
    damage = TLBMethods.getDouble("ExtraAbilities.NickC1211.AirJab.Damage", currentLevel);
    push = TLBMethods.getDouble("ExtraAbilities.NickC1211.AirJab.Push", currentLevel);
    maxBlasts = TLBMethods.getInt("ExtraAbilities.NickC1211.AirJab.MaxBlasts", currentLevel);
    collisionRadius = TLBMethods.getInt("ExtraAbilities.NickC1211.AirJab.CollisonRadius", currentLevel);
    
    origin = player.getLocation().clone().add(0.0D, 1.0D, 0.0D);
    location = origin.clone();
    direction = player.getLocation().getDirection();
  }
  
  public void progress()
  {
    if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
      remove();
      return;
    }
    if (origin.distance(this.location) > range || currentBlasts >= maxBlasts) {
      remove();
      bPlayer.addCooldown(this);
      return;
    }
    if (this.location.getBlock().getType().isSolid()) {
      remove();
      return;
    }
    if (this.location.getBlock().isLiquid()) {
      remove();
      return;
    }
    
    this.location.add(direction.multiply(speed));
    this.location.add(player.getLocation().getDirection());
    ParticleEffect.CLOUD.display(this.location, 6, 0.08D, 0.08D, 0.08D, 0.0D);
    ParticleEffect.SPELL.display(this.location, 2, 0.5D, 0.5D, 0.5D, 0.0D);
    currentBlasts++;
    if (new Random().nextInt(5) == 0) {
      playAirbendingSound(this.location);
    }
    
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, collisionRadius)) {
      if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId()) {
        remove();
        bPlayer.addCooldown(this);
        Location location = player.getEyeLocation();
        Vector vector = location.getDirection();
        entity.setVelocity(vector.normalize().multiply(push));
        DamageHandler.damageEntity(entity, damage, this);
        return;
      }
    }
  }
  
  public long getCooldown()
  {
    return cooldown;
  }
  
  public Location getLocation()
  {
    return null;
  }
  
  public String getName()
  {
    return "AirJab";
  }
  
  public String getDescription()
  {
    return "A basic air move that shoots a punch of air at your opponent.";
  }
  
  public String getInstructions()
  {
    return "Left CLick to shoot";
  }
  
  public boolean isHarmlessAbility()
  {
    return false;
  }
  
  public boolean isSneakAbility()
  {
    return false;
  }
  
  public String getAuthor()
  {
    return "NickC1211";
  }
  
  public String getVersion()
  {
    return "v1.2";
  }
  
  public void load()
  {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new AirJabListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + "by" + getAuthor() + "loaded!");
    
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.Cooldown", Integer.valueOf(2000));
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.Range", Integer.valueOf(20));
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.Damage", Integer.valueOf(2));
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.Speed", Float.valueOf(0.7F));
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.Push", Integer.valueOf(1));
    ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.AirJab.CollisionRadius", Integer.valueOf(1));
    ConfigManager.defaultConfig.save();
  }
  
  public void stop()
  {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new AirJabListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + "by" + getAuthor() + "disabled!");
  }
}
