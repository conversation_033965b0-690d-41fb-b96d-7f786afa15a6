package com.thelastblockbender.dustcloud;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class DustCloud extends SandAbility implements AddonAbility {
  private double radius;
  private long duration;
  private long cooldown;
  private int potDuration;
  private int invisDuration;

  private Location playerLocation;

  public DustCloud(final Player player) {
    super(player);
    if (!bPlayer.canBend(this) || hasAbility(player, DustCloud.class)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBMethods.getLong("Abilities.Varhagna.DustCloud.Cooldown", currentLevel);
    this.duration = TLBMethods.getLong("Abilities.Varhagna.DustCloud.Duration", currentLevel);
    this.radius = TLBMethods.getInt("Abilities.Varhagna.DustCloud.Radius", currentLevel);
    this.potDuration = TLBMethods.getInt("Abilities.Varhagna.DustCloud.EffectDuration", currentLevel);
    this.invisDuration = TLBMethods.getInt("Abilities.Varhagna.DustCloud.InvisibilityDuration", currentLevel);
    this.playerLocation = player.getLocation();

    start();
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return playerLocation;
  }

  @Override
  public String getName() {
    return "DustCloud";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreBinds(this) || System.currentTimeMillis() > getStartTime() + duration || !player.isSneaking()) {
      remove();
      return;
    }

    Block b = this.player.getLocation().subtract(0, 1, 0).getBlock();
    if (isEarthbendable(b)) {
      final Location center = playerLocation;
      ParticleEffect.BLOCK_DUST.display(center, 18, radius, radius, radius, b.getBlockData());
      for (final Entity e : GeneralMethods.getEntitiesAroundPoint(center, radius)) {
        if (e instanceof LivingEntity) {
          final LivingEntity entity = (LivingEntity) e;
          if (e.getEntityId() == player.getEntityId()) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, invisDuration / 50, 1));
          } else {
            if (!entity.hasPotionEffect(PotionEffectType.BLINDNESS)) {
              entity.addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, potDuration / 50, 1));
            }
            if (!entity.hasPotionEffect(PotionEffectType.SLOWNESS)) {
              entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, potDuration / 50, 1));
            }
          }
        }
      }
    }
  }

  @Override
  public String getAuthor() {
    return "TLB (Original: Varhagna)";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public String getDescription() {
    return "By shaking the ground back and forth, Earthbenders can create dust clouds to provide cover.";
  }

  @Override
  public String getInstructions() {
    return "Sneak while standing on a sufficient source of earth.";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new DustCloudListener(), ProjectKorra.plugin);
    ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.DustCloud.Cooldown", 4500);
    ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.DustCloud.Duration", 3500);
    ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.DustCloud.EffectDuration", 1500);
    ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.DustCloud.InvisibilityDuration", 3000);
    ConfigManager.defaultConfig.get().addDefault("Abilities.Varhagna.DustCloud.Radius", 5);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
  }
}
