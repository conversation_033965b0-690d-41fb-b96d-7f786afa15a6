package com.thelastblockbender.dustcloud;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class DustCloudListener implements Listener {
  @EventHandler(priority = EventPriority.NORMAL, ignoreCancelled = true)
  public void onSneak(final PlayerToggleSneakEvent event) {
    if (!event.isSneaking()) {
      return;
    }
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("DustCloud")) {
      new DustCloud(event.getPlayer());
    }
  }
}
