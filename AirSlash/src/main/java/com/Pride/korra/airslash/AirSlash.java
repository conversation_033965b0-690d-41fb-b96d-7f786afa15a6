package com.Pride.korra.AirSlash;


import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class AirSlash extends AirAbility implements AddonAbility {
	
	private ConcurrentHashMap<Location, Double> locations = new ConcurrentHashMap<Location, Double>();
	
	private long cooldown;
	private long threshold;
	private int mShots;
	private double range;
	private double damage;
	private boolean controllable;
	
	private int shots;
	private long lastShotTime;
	
	private Location location;
	private Vector direction;
	long currentLevel;

	public AirSlash(Player player) {
		super(player);
		
		if (!bPlayer.canBend(this)) {
			return;
		}
		
		if (bPlayer.isAvatarState()) {
			cooldown = 0;
			damage = 5;
		}
		
		if (hasAbility(player, AirSlash.class)) {
			AirSlash as = (AirSlash) getAbility(player, AirSlash.class);
			as.createShot();
			return;
		}
		
		setFields();
		direction = player.getEyeLocation().getDirection();

		modify();
		start();
		createShot();
	}
	
	private void modify() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		final List<String> EqualizedWorlds = getConfig().getStringList("Properties.EqualizedWorlds");
		if (EqualizedWorlds != null && EqualizedWorlds.contains(player.getWorld().getName())) {
			currentLevel = 5;
		}
		else {
			currentLevel = Manager.getManager(StatisticsManager.class).getStatisticCurrent(player.getUniqueId() , statLevel);
			
		}
		
		this.range = (int)((currentLevel * 0.65) + 5);
	}

	private void setFields() {
		cooldown = ConfigManager.getConfig().getLong("ExtraAbilities.Prride.AirSlash.Cooldown");
		range = ConfigManager.getConfig().getDouble("ExtraAbilities.Prride.AirSlash.Range");
		damage = ConfigManager.getConfig().getDouble("ExtraAbilities.Prride.AirSlash.Damage");
		controllable = ConfigManager.getConfig().getBoolean("ExtraAbilities.Prride.AirSlash.Controllable");
		
		mShots = 7;
		threshold = 500;
		location = player.getLocation().clone().add(0, 1, 0).clone();
		shots = mShots;
	}

	private void createShot() {
		if (shots >= 7) {
			lastShotTime = System.currentTimeMillis();
			shots--;
			locations.put(player.getEyeLocation().add(player.getLocation().getDirection().multiply(1.5).normalize()), 0D);
		}
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public String getName() {
		return "AirSlash";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return false;
	}

	@Override
	public void progress() {
		progressSlashes();
		if (player.isDead() || !player.isOnline()) {
			remove();
			return;
		}
		if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
			prepareRemove();
			return;
		}
		if (shots == 7 || System.currentTimeMillis() > lastShotTime + threshold) {
			prepareRemove();
			return;
		}
		return;
	}
	
	private void prepareRemove() {
		if (player.isOnline() && !bPlayer.isOnCooldown(this)) {
			bPlayer.addCooldown(this);
		}
		if (locations.isEmpty()) {
			remove();
			return;
		}
	}
	
	private void progressSlashes() {
		for (Location l : locations.keySet()) {
			Location loc = l.clone();
			double dist = locations.get(l);
			boolean cancel = false;
			for (int i = 0; i < 3; i++) {
				dist++;
				if (cancel || dist >= range) {
					cancel = true;
					break;
				}
				if (controllable) {
					direction = player.getLocation().getDirection();
				}
				loc = loc.add(direction.clone().multiply(1));
				if (GeneralMethods.isSolid(loc.getBlock()) || isWater(loc.getBlock()) || GeneralMethods.isRegionProtectedFromBuild(player, "AirSlash", loc)) {
					explosion(loc);
					cancel = true;
					break;
				}
				
				//Current Order: Location, Amount, OffsetX, OffsetY, OffsetZ (I think)
				ParticleEffect.SWEEP_ATTACK.display(loc, 2, (double) Math.random() / 5, (double) Math.random() / 5, (double) Math.random() / 5, 0);
				//ParticleEffect.SWEEP_ATTACK.display((float) Math.random() / 5, (float) Math.random() / 5, (float) Math.random() / 5, 0f, 2, loc, 257D);
				ParticleEffect.CLOUD.display(loc, 4, 0.125, 0.125, 0.125, 0.06);
				//ParticleEffect.CLOUD.display(loc, 0.125F, 0.125F, 0.125F, 0.06F, 4);
				//GeneralMethods.displayColoredParticle(loc, "bffffc", (float) Math.random() / 5, (float) Math.random() / 5, (float) Math.random() / 5);
				loc.getWorld().playSound(loc, Sound.ENTITY_PLAYER_ATTACK_SWEEP, 1f, 1f);

				for (Entity entity : GeneralMethods.getEntitiesAroundPoint(loc, 1.5)) {
					if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId() && !(entity instanceof ArmorStand)) {
						DamageHandler.damageEntity(entity, damage, this);
						//bleed(loc);
					}
				}
				Block block = location.getBlock();
				for (Block testblock : GeneralMethods.getBlocksAroundPoint(loc, 2)) {
					if (testblock.getType() == Material.FIRE) {
						testblock.setType(Material.AIR);
						testblock.getWorld().playEffect(testblock.getLocation(), Effect.EXTINGUISH, 0);
					}
					if (GeneralMethods.isRegionProtectedFromBuild(this, block.getLocation())) {
						continue;
					}
				}
			}

			if (cancel) {
				locations.remove(l);
			} else {
				locations.remove(l);
				locations.put(loc, dist);
			}
		}
	}
	
	public void explosion(Location loc) {
		ParticleEffect.CLOUD.display(loc, 20, Math.random(), Math.random(), Math.random(), 0.5);
		//ParticleEffect.CLOUD.display((float) Math.random(), (float) Math.random(), (float) Math.random(), 0.5f, 20, loc, 257D);
		ParticleEffect.EXPLOSION_NORMAL.display(loc, 5, Math.random(), Math.random(), Math.random(), 0.5);
		//ParticleEffect.EXPLODE.display((float) Math.random(), (float) Math.random(), (float) Math.random(), 0.5f, 5, loc, 257D);
		loc.getWorld().playSound(loc, Sound.BLOCK_WOOD_FALL, 1f, 1f);
	}
	
	public void bleed(Location loc) {
		ItemStack redstoneTexture = new ItemStack(Material.REDSTONE_BLOCK);
		ParticleEffect.ITEM_CRACK.display(loc, 20, Math.random(), Math.random(), Math.random(), 0.05, redstoneTexture);
		//ParticleEffect.BLOCK_CRACK.display(new ParticleEffect.BlockData(Material.REDSTONE_BLOCK, (byte) 0),(float) Math.random(), (float) Math.random(), (float) Math.random(), 0.5f, 20, loc, 257D);
		loc.getWorld().playSound(loc, Sound.ENTITY_WITCH_DRINK, 1f, 3f);
	}

	@Override
	public String getAuthor() {
		return "Prride (updated by DeminBell)";
	}

	@Override
	public String getVersion() {
		return "v1.1";
	}
	
	public String getDescription() {
		return "Airbenders can now bend air that can cut through opponents! It also extinguishes fire blocks!";
	}
	
	public String getInstructions() {
		return "Left click to shoot a slice attack through the air towards a target.";
	}

	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SlashListener(), ProjectKorra.plugin);
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");
		
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.AirSlash.Cooldown", 3000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.AirSlash.Range", 80);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.AirSlash.Damage", 4);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.AirSlash.Controllable", true);
		ConfigManager.defaultConfig.save();
	}

	@Override
	public void stop() {
		
	}

}
