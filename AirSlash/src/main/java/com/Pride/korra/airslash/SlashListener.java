package com.Pride.korra.AirSlash;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

import com.projectkorra.projectkorra.ability.CoreAbility;

public class SlashListener implements Listener {
	
	@EventHandler
	public void onSwing(PlayerAnimationEvent event) {
		if (event.isCancelled()) {
			return;
			
		} else if (CoreAbility.hasAbility(event.getPlayer(), AirSlash.class)) {
			return;
			
		}
		new AirSlash(event.getPlayer());
	}

}
