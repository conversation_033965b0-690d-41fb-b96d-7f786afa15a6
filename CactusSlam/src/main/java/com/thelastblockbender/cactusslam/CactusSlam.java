package com.thelastblockbender.cactusslam;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.BoundingBox;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

public class CactusSlam extends PlantAbility implements AddonAbility {
  private Location location;

  private long cooldown;
  private double damage;
  private double knockback;
  private double collisionRadius;
  private int nauseaAmp;
  private int nauseaDuration;

  public CactusSlam(Player player) {
    super(player);
    if (!bPlayer.canBendIgnoreBinds(this) || hasAbility(player, CactusSlam.class)) {
      return;
    }
    setFields();

    if (canActivate()) {
      bPlayer.addCooldown(this, cooldown);
      Block block = location.getBlock();
      if (block.getType() == Material.CACTUS) {
        location.getBlock().setType(Material.AIR);
      }
      explode();
      start();
    }
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Moros.CactusSlam.Cooldown", currentLevel);
    damage = TLBMethods.getDouble("ExtraAbilities.Moros.CactusSlam.Damage", currentLevel);
    knockback = TLBMethods.getDouble("ExtraAbilities.Moros.CactusSlam.ExplosionKnockBack", currentLevel);
    collisionRadius = TLBMethods.getDouble("ExtraAbilities.Moros.CactusSlam.CollisionRadius", currentLevel);
    nauseaAmp = TLBMethods.getInt("ExtraAbilities.Moros.CactusSlam.NauseaAmplifier", currentLevel) - 1;
    nauseaDuration = TLBMethods.getInt("ExtraAbilities.Moros.CactusSlam.NauseaDuration", currentLevel);
  }

  private boolean canActivate() {
    BoundingBox box = player.getBoundingBox().expand(2);
    World world = player.getWorld();
    Location center = entityCenter(player);
    double minDistSq = 100;
    for (double x = box.getMinX(); x <= box.getMaxX(); x++) {
      for (double y = box.getMinY(); y <= box.getMaxY(); y++) {
        for (double z = box.getMinZ(); z <= box.getMaxZ(); z++) {
          int blockX = NumberConversions.floor(x);
          int blockY = NumberConversions.floor(y);
          int blockZ = NumberConversions.floor(z);
          Block block = world.getBlockAt(blockX, blockY, blockZ);
          if (block.getType() == Material.CACTUS) {
            Location temp = new Location(world, blockX + 0.5, blockY + 0.5, blockZ + 0.5);
            double distSq = center.distanceSquared(temp);
            if (distSq < minDistSq) {
              minDistSq = distSq;
              location = temp.clone();
            }
          }
        }
      }
    }
    return location != null;
  }

  @Override
  public void progress() {
    double offset = 0.75 * collisionRadius;
    int amount = Math.max(1, NumberConversions.floor(offset));
    ParticleEffect.BLOCK_CRACK.display(location, 16 * amount, offset, offset, offset, 0.05, Material.CACTUS.createBlockData());
    ParticleEffect.COMPOSTER.display(location, 8 * amount, offset, offset, offset);
    location.getWorld().spawnParticle(Particle.GLOW_SQUID_INK, location, 24 * amount, offset, offset, offset, 0.2 * collisionRadius, null, true);
    location.getWorld().playSound(location, Sound.BLOCK_WOOL_BREAK, 2.5F, 1.0F);
    remove();
  }

  private void explode() {
    for (Entity e : GeneralMethods.getEntitiesAroundPoint(location, collisionRadius)) {
      if (e instanceof ArmorStand || e instanceof Player && Commands.invincible.contains((e).getName())) {
        return;
      }
      if (e instanceof LivingEntity livingEntity) {
        DamageHandler.damageEntity(livingEntity, player, damage, this);
        livingEntity.addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, nauseaDuration / 50, nauseaAmp));
      }
      if (e.getEntityId() != player.getEntityId()) {
        Vector dir = GeneralMethods.getDirection(location.clone(), entityCenter(e));
        e.setVelocity(dir.clone().normalize().multiply(knockback));
      }
    }
  }

  private Location entityCenter(Entity entity) {
    return entity.getLocation().add(0, entity.getHeight() / 2, 0);
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return player.getLocation();
  }

  @Override
  public String getName() {
    return "CactusSlam";
  }

  @Override
  public String getDescription() {
    return "Cactus SLAM!";
  }

  @Override
  public String getInstructions() {
    return "Boink!";
  }

  @Override
  public boolean isHiddenAbility() {
    return true;
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "Moros";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  private static CactusSlamListener listener;

  @Override
  public void load() {
    if (listener != null) {
      listener.unregister();
    }
    listener = new CactusSlamListener();
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(listener, ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.Cooldown", 15000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.Damage", 2.0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.ExplosionKnockBack", 1.6);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.CollisionRadius", 3.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.NauseaAmplifier", 3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Moros.CactusSlam.NauseaDuration", 7500);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
    if (listener != null) {
      listener.unregister();
    }
  }
}
