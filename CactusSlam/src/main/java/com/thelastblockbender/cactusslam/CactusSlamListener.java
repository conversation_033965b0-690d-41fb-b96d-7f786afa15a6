package com.thelastblockbender.cactusslam;

import com.projectkorra.projectkorra.event.HorizontalVelocityChangeEvent;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityDamageEvent.DamageCause;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class CactusSlamListener implements Listener {
  @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
  public void onDamage(EntityDamageEvent event) {
    if (event.getCause() == DamageCause.FLY_INTO_WALL && event.getEntity() instanceof Player player) {
      if (new CactusSlam(player).isStarted()) {
        event.setCancelled(true);
      }
    }
  }

  @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
  public void onDamage(HorizontalVelocityChangeEvent e) {
    if (e.getEntity() instanceof Player player && new CactusSlam(player).isStarted()) {
      e.setCancelled(true);
    }
  }

  void unregister() {
    PlayerToggleSneakEvent.getHandlerList().unregister(this);
    PlayerAnimationEvent.getHandlerList().unregister(this);
  }
}
