package com.thelastblockbender.razorring;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;

public class RazorRing<PERSON>istener implements Listener {
  @EventHandler
  public void onSneak(PlayerToggleSneakEvent event) {
    if (event.isCancelled() || !event.isSneaking()) {
      return;
    }
    Player player = event.getPlayer();
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("RazorRing")) {
      new RazorRing(player);
    }
  }

  @EventHandler(ignoreCancelled = true)
  public void onSwing(PlayerAnimationEvent event) {
    Player player = event.getPlayer();
    if (BendingPlayer.getBendingPlayer(player).getBoundAbilityName().equalsIgnoreCase("RazorRing")) {
      RazorRing.freeze(player);
    }
  }

  void unregister() {
    PlayerToggleSneakEvent.getHandlerList().unregister(this);
    PlayerAnimationEvent.getHandlerList().unregister(this);
  }
}