package com.thelastblockbender.mist;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.block.Action;

import org.bukkit.event.player.PlayerToggleSneakEvent;


public class MistListener implements org.bukkit.event.Listener {
  @EventHandler
  public void onSneak(PlayerToggleSneakEvent event) { //on sneak:

    if (event.isCancelled()) {
      return;
    }
    if (!event.isSneaking()) {
        return;
    }

    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer()); //ensure player is valid
    if (bPlayer == null) {return;}

    String abilityName = bPlayer.getBoundAbilityName(); //check selected ability is correct
    if (abilityName.equalsIgnoreCase("Mist")) {
      new Mist(event.getPlayer()); //start move
    }
  }
}