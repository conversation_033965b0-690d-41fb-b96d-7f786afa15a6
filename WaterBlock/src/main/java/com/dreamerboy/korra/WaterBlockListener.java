package com.dreamerboy.korra;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.event.AbilityCollisionEvent;
import com.projectkorra.projectkorra.waterbending.Torrent;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.util.Vector;

public class WaterBlockListener implements Listener {
   @EventHandler
   public void onCollision(AbilityCollisionEvent e) {
      Player player;
      WaterBlock wb;
      Vector dir;
      Vector otherVec;
      double angle;
      if (e.getCollision().getAbilityFirst().getName().equals("WaterBlock")) {
         player = e.getCollision().getAbilityFirst().getPlayer();
         wb = (WaterBlock)CoreAbility.getAbility(player, WaterBlock.class);
         if (e.getCollision().getAbilitySecond().getName().equals("Torrent") && !((Torrent)e.getCollision().getAbilitySecond()).isLaunching()) {
            e.setCancelled(true);
         }

         dir = player.getLocation().getDirection();
         otherVec = e.getCollision().getAbilitySecond().getLocation().toVector().subtract(player.getLocation().toVector());
         angle = Math.acos(dir.dot(otherVec) / (dir.length() * otherVec.length()));
         angle = Math.toDegrees(angle);
         if (angle > 60.0D) {
            player.sendMessage(Element.WATER.getColor() + "Because of an attack that is out of the sight view, ability got cancelled.");
            if (wb.isCooldownOnOutOfTheSightView() && !wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            wb.remove();
            e.setCancelled(true);
         } else if (e.getCollision().getAbilitySecond().getName().equals("WaterManipulation")) {
            wb.setRange(wb.getWaterManipulationRange());
            wb.setLocation(e.getCollision().getAbilitySecond().getLocation());
            wb.effects(e.getCollision().getAbilitySecond().getLocation());
            wb.setCount(wb.getCount() + 1);
            if (!wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            if (wb.getCount() >= 3) {
               ((WaterBlock)CoreAbility.getAbility(player, WaterBlock.class)).remove();
            }
         } else {
            wb.setRange(ConfigManager.getConfig().getDouble("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.Others"));
            wb.setLocation(e.getCollision().getAbilitySecond().getLocation());
            wb.effects(e.getCollision().getAbilitySecond().getLocation());
            if (!wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            ((WaterBlock)CoreAbility.getAbility(player, WaterBlock.class)).remove();
         }
      } else if (e.getCollision().getAbilitySecond().getName().equals("WaterBlock")) {
         player = e.getCollision().getAbilitySecond().getPlayer();
         wb = (WaterBlock)CoreAbility.getAbility(player, WaterBlock.class);
         if (e.getCollision().getAbilityFirst().getName().equals("Torrent") && !((Torrent)e.getCollision().getAbilityFirst()).isLaunching()) {
            e.setCancelled(true);
         }

         dir = player.getLocation().getDirection();
         otherVec = e.getCollision().getAbilityFirst().getLocation().toVector().subtract(player.getLocation().toVector());
         angle = Math.acos(dir.dot(otherVec) / (dir.length() * otherVec.length()));
         angle = Math.toDegrees(angle);
         if (angle > 60.0D) {
            player.sendMessage(Element.WATER.getColor() + "Because of an attack that is out of the sight view, ability got cancelled.");
            if (wb.isCooldownOnOutOfTheSightView() && !wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            wb.remove();
            e.setCancelled(true);
         } else if (e.getCollision().getAbilityFirst().getName().equals("WaterManipulation")) {
            wb.setRange(wb.getWaterManipulationRange());
            wb.setLocation(e.getCollision().getAbilityFirst().getLocation());
            wb.effects(e.getCollision().getAbilityFirst().getLocation());
            wb.setCount(wb.getCount() + 1);
            if (!wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            if (wb.getCount() >= 3) {
               ((WaterBlock)CoreAbility.getAbility(player, WaterBlock.class)).remove();
            }
         } else {
            wb.setRange(ConfigManager.getConfig().getDouble("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.Others"));
            wb.setLocation(e.getCollision().getAbilityFirst().getLocation());
            wb.effects(e.getCollision().getAbilityFirst().getLocation());
            if (!wb.isDoneWork()) {
               wb.setDoneWork(true);
            }

            ((WaterBlock)CoreAbility.getAbility(player, WaterBlock.class)).remove();
         }
      }

   }
}
