package com.dreamerboy.korra;

import java.util.ArrayList;
import java.util.Iterator;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;
import com.projectkorra.projectkorra.ability.util.Collision;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.SurgeWave;
import com.projectkorra.projectkorra.waterbending.Torrent;
import com.projectkorra.projectkorra.waterbending.WaterManipulation;

public class WaterBlock extends WaterAbility implements AddonAbility, ComboAbility {
   private int count = 0;
   private long cooldown;
   private long duration;
   private String path = "ExtraAbilities.DreamerBoy.Water.WaterBlock.";
   private double radius;
   private double knockback;
   private double range;
   private double manipRange;
   private Location location;
   private boolean cooldownOnOutOfTheSightView;
   private boolean doneWork = false;
   private Listener WBL;

   public WaterBlock(Player player) {
      super(player);
      if (this.bPlayer.canBendIgnoreBinds(this) && !hasAbility(player, WaterBlock.class)) {
         this.setFields();
         this.start();
      }
   }

   private void setFields() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      long currentLevel = TLBMethods.limitLevels(this.player, statLevel);
      this.cooldown = TLBMethods.getLong(String.valueOf(this.path) + "Cooldown.Cooldown", currentLevel);
      this.cooldownOnOutOfTheSightView = ConfigManager.getConfig().getBoolean(String.valueOf(this.path) + "Cooldown.CooldownOnOutOfTheSightView");
      this.duration = TLBMethods.getLong(String.valueOf(this.path) + "Duration", currentLevel);
      this.knockback = TLBMethods.getDouble(String.valueOf(this.path) + "Knockback", currentLevel);
      this.radius = TLBMethods.getDouble(String.valueOf(this.path) + "Radius", currentLevel);
      this.manipRange = TLBMethods.getDouble(String.valueOf(this.path) + "Range.WaterManipulation", currentLevel);
      this.range = TLBMethods.getDouble(String.valueOf(this.path) + "Range.Others", currentLevel);
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return this.player.getLocation();
   }

   public String getName() {
      return "WaterBlock";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public void remove() {
      if (this.doneWork) {
         this.bPlayer.addCooldown(this);
      }

      super.remove();
   }

   public void progress() {
      if (this.player.isSneaking() && this.bPlayer.canBendIgnoreBinds(this) && (this.duration <= 0L || System.currentTimeMillis() <= this.getStartTime() + this.duration)) {
         this.collision();
      } else {
         this.remove();
      }
   }

   public double getCollisionRadius() {
      return this.radius;
   }

   private void collision() {
      CoreAbility Torrent = CoreAbility.getAbility(Torrent.class);
      CoreAbility WaterManipulation = CoreAbility.getAbility(WaterManipulation.class);
      CoreAbility WaterBlock = CoreAbility.getAbility(WaterBlock.class);
      CoreAbility Surge = CoreAbility.getAbility(SurgeWave.class);
      CoreAbility[] cAbilities = new CoreAbility[]{Torrent, WaterManipulation, Surge};
      CoreAbility[] array = cAbilities;
      int length = cAbilities.length;

      for(int i = 0; i < length; ++i) {
         CoreAbility cA = array[i];
         ProjectKorra.getCollisionManager().addCollision(new Collision(WaterBlock, cA, false, true));
      }

   }

   public void handleCollision(Collision collision) {
      super.handleCollision(collision);
   }

   public boolean isDoneWork() {
      return this.doneWork;
   }

   public void setDoneWork(boolean doneWork) {
      this.doneWork = doneWork;
   }

   public void setRange(double range) {
      this.range = range;
   }

   public int getCount() {
      return this.count;
   }

   public void setCount(int count) {
      this.count = count;
   }

   public double getRange() {
      return this.range;
   }

   public void effects(final Location loc) {
      (new BukkitRunnable() {
         double r = 0.0D;

         public void run() {
            if (this.r < WaterBlock.this.range) {
               WaterBlock.this.effect(WaterBlock.this.location, this.r);
               Vector tmpVector = WaterBlock.this.player.getLocation().toVector().subtract(loc.toVector()).normalize();
               WaterBlock.this.location.add(tmpVector.multiply(-1));
               ++this.r;
            } else {
               this.cancel();
            }

         }
      }).runTaskTimer(ProjectKorra.plugin, 0L, 1L);
   }

   public void effect(Location loc, double r) {
      for(double theta = 0.0D; theta < 360.0D; theta += 20.0D) {
         Vector vector2;
         if (loc.getDirection().getX() == -0.0D && loc.getDirection().getY() == -0.0D && loc.getDirection().getZ() == 1.0D) {
            vector2 = this.player.getLocation().toVector().subtract(loc.toVector()).normalize();
            Vector vector = GeneralMethods.getOrthogonalVector(vector2, theta, r);
            Location location2 = loc.clone().add(vector);
            if (isAir(location2.getBlock().getType()) || isWater(location2.getBlock()) && TempBlock.isTempBlock(location2.getBlock())) {
               (new TempBlock(location2.getBlock(), Material.WATER)).setRevertTime(1000L);
               ParticleEffect.DRIP_WATER.display(location2, 1, 0.0D, 0.0D, 0.0D, 0.05999999865889549D);
               ParticleEffect.WATER_DROP.display(location2, 1, 0.0D, 0.0D, 0.0D, 0.05999999865889549D);
               Iterator var10 = GeneralMethods.getEntitiesAroundPoint(this.location, 3.0D).iterator();

               while(var10.hasNext()) {
                  Entity entity = (Entity)var10.next();
                  if (entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId()) {
                     GeneralMethods.setVelocity(entity, entity.getLocation().toVector().subtract(this.location.toVector()).normalize().multiply(this.knockback));
                  }
               }
            }
         } else {
            vector2 = GeneralMethods.getOrthogonalVector(loc.getDirection(), theta, r);
            Location location3 = loc.clone().add(vector2);
            if (isAir(location3.getBlock().getType()) || isWater(location3.getBlock()) && TempBlock.isTempBlock(location3.getBlock())) {
               (new TempBlock(location3.getBlock(), Material.WATER)).setRevertTime(1000L);
               ParticleEffect.DRIP_WATER.display(location3, 1, 0.0D, 0.0D, 0.0D, 0.05999999865889549D);
               ParticleEffect.WATER_DROP.display(location3, 1, 0.0D, 0.0D, 0.0D, 0.05999999865889549D);
               Iterator var9 = GeneralMethods.getEntitiesAroundPoint(this.location, 3.0D).iterator();

               while(var9.hasNext()) {
                  Entity entity2 = (Entity)var9.next();
                  if (entity2 instanceof LivingEntity && entity2.getEntityId() != this.player.getEntityId()) {
                     GeneralMethods.setVelocity(entity2, entity2.getLocation().toVector().subtract(this.location.toVector()).normalize().multiply(this.knockback));
                  }
               }
            }
         }
      }

   }

   public Object createNewComboInstance(Player player) {
      return new WaterBlock(player);
   }

   public ArrayList<AbilityInformation> getCombination() {
      ArrayList<AbilityInformation> combo = new ArrayList();
      combo.add(new AbilityInformation("WaterBubble", ClickType.SHIFT_DOWN));
      combo.add(new AbilityInformation("WaterBubble", ClickType.LEFT_CLICK));
      return combo;
   }

   public String getDescription() {
      return "This ability makes you be able to re-manipulate the water within which the abilities that are called Torrent, WaterManipulation and Surge. Nevertheless, these abilities casted upon you need to be in sight of yours which means you can not block the Torrent coming behind of you. In addition to this, you could block WaterManipulation ability 3 times in-a-row.";
   }

   public String getInstructions() {
      return "WaterBubble (Hold Sneak) > WaterBubble (Left Click)";
   }

   public String getAuthor() {
      return "DreamerBoy & Hiro3";
   }

   public String getVersion() {
      return "1.2";
   }

   public double getWaterManipulationRange() {
      return this.manipRange;
   }

   public void setWaterManipulationRange(double waterManipulationRange) {
      this.manipRange = waterManipulationRange;
   }

   public Location getLoc() {
      return this.location;
   }

   public boolean isCooldownOnOutOfTheSightView() {
      return this.cooldownOnOutOfTheSightView;
   }

   public void setLocation(Location value) {
      this.location = value;
   }

   public void load() {
      this.WBL = new WaterBlockListener();
      ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " enabled! ");
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(this.WBL, ProjectKorra.plugin);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Cooldown.Cooldown", 8000);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Cooldown.CooldownOnOutOfTheSightView", true);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Duration", 0);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Knockback", 2);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Radius", 2);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.WaterManipulation", 4);
      ConfigManager.getConfig().addDefault("ExtraAbilities.DreamerBoy.Water.WaterBlock.Range.Others", 8);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " disabled! ");
      HandlerList.unregisterAll(this.WBL);
      super.remove();
   }
}
