
package com.thelastblockbender.condensation;

import java.util.ArrayList;
import java.util.List;

import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.boss.BarColor;
import org.bukkit.boss.BarFlag;
import org.bukkit.boss.BarStyle;
import org.bukkit.boss.BossBar;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.PKListener;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.BlockSource;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

public class Condensation extends WaterAbility implements AddonAbility, ComboAbility {
   private BossBar chargeBar;
   private double chargeAmount = 0.025D;
   private double chargeCount;
   private boolean started;
   private boolean formedSource;
   private Location center;
   private TempBlock spinningBlock;
   private double radius;
   private double angle = 3.141592653589793D;
   private TempBlock taperedSourceBlock;
   private double rainCharge;
   private double sourceCharge;
   private double mistCharge;
   private double selectRange;
   private long currentLevel;
   private long cooldown;
   public static List<TempBlock> sourcedBlocks = new ArrayList();
   public static List<TempBlock> taperedSource = new ArrayList();

   public Condensation(Player player) {
      super(player);
      if (!CoreAbility.hasAbility(player, Condensation.class)) {
         if (!this.player.isDead() && this.player.isOnline() && !this.player.getGameMode().equals(GameMode.SPECTATOR)) {
            if (this.bPlayer != null) {
               if (!this.bPlayer.isOnCooldown("Condensation")) {
                  if (this.bPlayer.canBendIgnoreBindsCooldowns(this)) {
                     int statLevel = StatisticsMethods.getId("AbilityLevel_Condensation");
                     this.currentLevel = TLBMethods.limitLevels(player, statLevel);
                     this.cooldown = TLBMethods.getLong("Abilities.Water.Condensation.Cooldown", this.currentLevel);
                     this.radius = (double)TLBMethods.getLong("Abilities.Water.Condensation.BuddyDistance", this.currentLevel);
                    this.mistCharge = TLBMethods.getDouble("Abilities.Water.Condensation.MistChargeSpeed", this.currentLevel);
                     this.rainCharge = TLBMethods.getDouble("Abilities.Water.Condensation.RainChargeSpeed", this.currentLevel);
                     this.sourceCharge = TLBMethods.getDouble("Abilities.Water.Condensation.SourceChargeSpeed", this.currentLevel);
                     this.chargeAmount = TLBMethods.getDouble("Abilities.Water.Condensation.BaseChargeSpeed", this.currentLevel);
                     this.selectRange = (double)TLBMethods.getLong("Abilities.Water.Condensation.SourceRange", this.currentLevel);
                     if (!PKListener.remoteWaterSource.containsKey(player)) {
                        this.start();
                     }

                  }
               }
            }
         }
      }
   }



    private boolean isInsideMist(Entity entity) {
        String entityName = entity.getName();
        if (entity.getType() == EntityType.INTERACTION && entityName.substring(0,4).equals("MIST")) { //if they are a MIST marker
            if ( Double.parseDouble( entityName.substring(5,entityName.length())) >= player.getLocation().distance(entity.getLocation()) )
            return true;
        }
        return false;
    }

    private boolean checkMist() {
        List<Entity> validEntities = GeneralMethods.getEntitiesAroundPoint(player.getLocation(), 25) //25 because no Mist should ever go above that radius
            .stream()
            .filter(this::isInsideMist)
            .toList();
        if (validEntities.size() >= 1) { //at least one valid mist in range
            return true;
        }
        return false;
    }
    

   public void progress() {
      if (this.player.isSneaking()) {
         if (!this.started) {
            if (this.player.getWorld().hasStorm()) {
               this.chargeAmount = this.rainCharge;
            } else if (BlockSource.getWaterSourceBlock(this.player, this.selectRange, ClickType.SHIFT_DOWN, true, true, true) != null) {
               this.chargeAmount = this.sourceCharge;
            } else {
               this.chargeAmount = this.chargeAmount;
            }

            if (!this.bPlayer.getBoundAbilityName().equals("WaterManipulation")) {
               return;
            }

            this.started = true;
            this.chargeCount = 0.0D;
            this.chargeBar = Bukkit.getServer().createBossBar("", BarColor.WHITE, BarStyle.SOLID, new BarFlag[0]);
            this.chargeBar.setProgress(0.0D);
            this.chargeBar.addPlayer(this.player);
         } else {
            this.updateChargeBarProgress();
            if (this.chargeBar.getProgress() == 1.0D) {
               this.chargeBar.setColor(BarColor.BLUE);
               this.chargeBar.setTitle("CHARGED!");
            }
         }
      } else if (!this.formedSource) {
         this.remove();
         return;
      }

      if (this.formedSource) {
         this.center = this.player.getLocation().add(0.0D, 2.0D, 0.0D);
         this.displayParticles();
      }

   }

   public void remove() {
      if (this.started) {
         this.chargeBar.removeAll();
      }

      if (!taperedSource.isEmpty()) {
         ((TempBlock)taperedSource.get(0)).revertBlock();
         taperedSource.clear();
      }

      if (!sourcedBlocks.isEmpty()) {
         ((TempBlock)sourcedBlocks.get(0)).revertBlock();
         sourcedBlocks.clear();
      }

      PKListener.remoteWaterSource.remove(this.player);
      super.remove();
   }

   public void formSource() {
      if (this.chargeBar.getProgress() == 1.0D && !PKListener.remoteWaterSource.containsKey(this.player)) {
         this.chargeBar.removeAll();
         this.bPlayer.addCooldown(this);
         this.formedSource = true;
      }

   }

   public void displayParticles() {
      if (!this.bPlayer.canBendIgnoreBindsCooldowns(this)) {
         this.remove();
      } else {
         Block rightBlock = this.player.getEyeLocation().add(getRightHeadDirection(this.player).multiply(this.radius)).getBlock();
         if (rightBlock.getType() == Material.AIR || rightBlock.getType() == Material.CAVE_AIR || rightBlock.getType() == Material.WATER) {
            if (!taperedSource.isEmpty()) {
               ((TempBlock)taperedSource.get(0)).revertBlock();
               taperedSource.clear();
            }

            if (!sourcedBlocks.isEmpty()) {
               if (!((TempBlock)sourcedBlocks.get(0)).getLocation().equals(rightBlock.getLocation())) {
                  this.taperedSourceBlock = new TempBlock(((TempBlock)sourcedBlocks.get(0)).getLocation().getBlock(), GeneralMethods.getWaterData(7));
                  taperedSource.add(this.taperedSourceBlock);
               } else {
                  ((TempBlock)sourcedBlocks.get(0)).revertBlock();
               }
            }

            sourcedBlocks.clear();
            this.spinningBlock = new TempBlock(rightBlock, GeneralMethods.getWaterData(0));  // Level 0 = infinite source
            sourcedBlocks.add(this.spinningBlock);
            PKListener.remoteWaterSource.put(this.player, this.spinningBlock);
         }

      }
   }

   public static Vector getRightHeadDirection(Player player) {
      Vector direction = player.getLocation().getDirection().normalize();
      return (new Vector(-direction.getZ(), 0.0D, direction.getX())).normalize();
   }

   private void updateChargeBarProgress() {
      if (this.started) {
         if (this.chargeBar.getProgress() + this.chargeAmount <= 0.9999999D) {
            this.chargeBar.setProgress(this.chargeBar.getProgress() + this.chargeAmount);
            this.chargeBar.setTitle((int)(this.chargeBar.getProgress() * 100.0D) + "%");
         } else {
            this.chargeBar.setProgress(1.0D);
         }
      }

   }

   public Object createNewComboInstance(Player player) {
      return new Condensation(player);
   }

   public ArrayList<AbilityInformation> getCombination() {
      ArrayList<AbilityInformation> combo = new ArrayList();
      combo.add(new AbilityInformation("WaterManipulation", ClickType.SHIFT_DOWN));
      combo.add(new AbilityInformation("WaterManipulation", ClickType.SHIFT_UP));
      combo.add(new AbilityInformation("WaterManipulation", ClickType.SHIFT_DOWN));
      return combo;
   }

   public String getDescription() {
      return "Concentrate and draw water out of the air to create a portable water source! When used in the rain, it will charge almost instantly. Carry the water source around with you until you need it, or use it to block an incoming attack!";
   }

   public String getInstructions() {
      return "WaterManipulation (Tap Sneak) > WaterManipulation (Hold Sneak) > Punch when charged to create a water source.";
   }

   public String getAuthor() {
      return "TLB";
   }

   public String getVersion() {
      return "1.0";
   }

   public void load() {
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.Cooldown", 1000);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.BuddyDistance", 2);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.RainChargeSpeed", 0.025D);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.MistChargeSpeed", (Object)0.1);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.SourceChargeSpeed", 0.0175D);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.SourceRange", 4);
      ConfigManager.getConfig().addDefault("Abilities.Water.Condensation.BaseChargeSpeed", 0.005D);
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new CondensationListener(), ProjectKorra.plugin);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
      ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " disabled! ");
      super.remove();
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return null;
   }

   public String getName() {
      return "Condensation";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return false;
   }
}
