package com.thelastblockbender.condensation;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import com.projectkorra.projectkorra.PKListener;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class CondensationListener implements Listener {
   @EventHandler
   public void onSwing(PlayerAnimationEvent event) {
      if (!event.isCancelled()) {
         if (CoreAbility.hasAbility(event.getPlayer(), Condensation.class)) {
            Condensation abil = (Condensation)CoreAbility.getAbility(event.getPlayer(), Condensation.class);
            abil.formSource();
         }

      }
   }

   @EventHandler
   public void onPlayerQuit(PlayerQuitEvent event) {
      Player player = event.getPlayer();
      if (PKListener.remoteWaterSource.contains<PERSON>ey(player)) {
         PKListener.remoteWaterSource.remove(player);
         CoreAbility playerAbility = CoreAbility.getAbility(player, CoreAbility.getAbility("Condensation").getClass());
         playerAbility.remove();
      }

   }

   @EventHandler(
      priority = EventPriority.NORMAL,
      ignoreCancelled = true
   )
   public void onEntityDeath(EntityDeathEvent event) {
      if (event.getEntity() instanceof Player) {
         Player player = (Player)event.getEntity();
         if (PKListener.remoteWaterSource.containsKey(player)) {
            PKListener.remoteWaterSource.remove(player);
            CoreAbility playerAbility = CoreAbility.getAbility(player, CoreAbility.getAbility("Condensation").getClass());
            playerAbility.remove();
         }
      }

   }
}
