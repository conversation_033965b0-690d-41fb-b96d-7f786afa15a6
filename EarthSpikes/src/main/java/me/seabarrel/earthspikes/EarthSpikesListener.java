package me.seabarrel.earthspikes;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class EarthSpikesListener implements Listener {

    @EventHandler
    public void onShift(PlayerToggleSneakEvent event) {
        if (event.isSneaking()) {

            Player player = event.getPlayer();
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
            if (bPlayer == null) return;

            if (bPlayer.getBoundAbilityName().equalsIgnoreCase("EarthSpikes")) {
                if (CoreAbility.hasAbility(player, EarthSpikes.class)) {
                    if (!CoreAbility.getAbility(player, EarthSpikes.class).isStarted()) {
                        CoreAbility.getAbility(player, EarthSpikes.class).changeSource();
                    } else if (CoreAbility.getAbility(player, EarthSpikes.class).isStopped() && !bPlayer.isOnCooldown("EarthSpikes")) {
                        CoreAbility.getAbility(player, EarthSpikes.class).remove();
                        new EarthSpikes(player);
                    }
                } else {
                    new EarthSpikes(player);
                }
            }

        }
    }

    @EventHandler
    public void onClick(PlayerInteractEvent event) {
        if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer == null) return;

            if (bPlayer.getBoundAbilityName().equalsIgnoreCase("EarthSpikes")) {
                if (CoreAbility.hasAbility(event.getPlayer(), EarthSpikes.class)) {
                    CoreAbility.getAbility(event.getPlayer(), EarthSpikes.class).onClick();
                }
            }
        }
    }

}
