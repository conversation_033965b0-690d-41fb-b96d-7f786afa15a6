package me.seabarrel.earthspikes;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ability.ElementalAbility;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.data.type.PointedDripstone;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.HashSet;

public class Spike {

    private static HashSet<Material> transparent;

    private Location start;
    private int height;
    private ArrayList<TempBlock> blocks = new ArrayList<>();
    private boolean reverting = false;
    private long startTime;

    private EarthSpikes ability;

    public Spike(Location start, int height, EarthSpikes ability) {
        this.start = start;
        this.height = height;
        this.ability = ability;
        blocks.add(new TempBlock(start.getBlock(), Material.DRIPSTONE_BLOCK));
        startTime = System.currentTimeMillis();
    }

    public boolean update() {
        if (!isReverting()) {
            build();
            return false;
        } else {
            return revert();
        }
    }

    public void build() {
        if (isReverting() || blocks.size() >= height) {
            return;
        }
        this.start = start.add(0, 1, 0);
        if (!transparent.contains(start.getBlock().getType())) {
            height = blocks.size();
            return;
        }

        if (ElementalAbility.isWater(start.getBlock())) ability.removeWithCooldown();

        for (Entity entity : GeneralMethods.getEntitiesAroundPoint(start, 1.5)) {
            if (entity instanceof ArmorStand || entity instanceof Item) continue;
            entity.setVelocity(new Vector(0, 1, 0).multiply(ability.getKnockUp()));
            if (entity instanceof Player) {
                if (((Player) entity) == ability.getPlayer()) continue;
            }
            if (ability.getDamaged().contains(entity)) continue;
            ability.addDamaged(entity);
            DamageHandler.damageEntity(entity, ability.getDamage(), ability);
        }
        if (ability.getDamaged().size() > 0) ability.removeWithCooldown();
        blocks.add(new TempBlock(start.clone().getBlock(), Material.POINTED_DRIPSTONE));
        setNBT();
    }

    public void setNBT() {

        if (blocks.size() < 2) {
            return;
        }

        PointedDripstone data = (PointedDripstone)Material.POINTED_DRIPSTONE.createBlockData();

        data.setThickness(PointedDripstone.Thickness.TIP);
        blocks.get(blocks.size() - 1).getBlock().setBlockData(data);

        if (blocks.size() >= 3) {
            data.setThickness(PointedDripstone.Thickness.FRUSTUM);
            blocks.get(blocks.size() - 2).getBlock().setBlockData(data);

            if (blocks.size() >= 4) {
                data.setThickness(PointedDripstone.Thickness.BASE);
                blocks.get(1).getBlock().setBlockData(data);

                data.setThickness(PointedDripstone.Thickness.MIDDLE);
                for (int x = 2; x < blocks.size() - 2; x++) {
                    blocks.get(x).getBlock().setBlockData(data);
                }
            }
        }
    }

    public boolean revert() {
        if (blocks.size() < 1) return true;

        blocks.get(blocks.size() - 1).revertBlock();
        blocks.remove(blocks.size() - 1);
        setNBT();
        return blocks.size() < 1;
    }

    public void revertAll() {
        for (TempBlock tempBlock : blocks) {
            tempBlock.revertBlock();
        }
        blocks.clear();
    }

    public boolean isReverting() {
        return reverting;
    }

    public void setReverting(boolean revert) {
        this.reverting = revert;
    }

    public long getStartTime() {
        return this.startTime;
    }

    public boolean reachedHeight() {
        return blocks.size() >= height;
    }

    public static void setTransparent(HashSet<Material> newTransparent) {
        transparent = newTransparent;
    }

    public Location getLocation() {
        return start.subtract(0, blocks.size() - 1, 0);
    }
}
