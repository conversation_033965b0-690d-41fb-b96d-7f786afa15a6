package me.seabarrel.earthspikes;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.configuration.Configuration;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import java.util.ArrayList;

public class EarthSpikes extends EarthAbility implements AddonAbility {

    @Attribute(Attribute.COOLDOWN)
    private long cooldown;
    @Attribute(Attribute.RANGE)
    private int range;
    @Attribute(Attribute.DAMAGE)
    private double damage;
    @Attribute("MAX_HEIGHT")
    private double maxHeight;
    @Attribute(Attribute.SELECT_RANGE)
    private int sourceRange;
    @Attribute("INTERVAL")
    private long interval;
    @Attribute(Attribute.DURATION)
    private long spikeDuration;
    @Attribute(Attribute.KNOCKUP)
    private double knockUp;

    private TempBlock sourceBlock;
    private Location location;
    private ArrayList<Spike> spikes = new ArrayList<>();
    private boolean started = false;
    private boolean stopped = false;
    private Vector direction;
    private int traveled = 0;
    private long time = System.currentTimeMillis();

    private ArrayList<Entity> damaged = new ArrayList<>();

    public EarthSpikes(Player player) {
        super(player);

        if (CoreAbility.hasAbility(player, this.getClass())) return;

        Spike.setTransparent(getTransparentMaterialSet());

        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);
        cooldown = TLBMethods.getLong("ExtraAbilities.Seabarrel.EarthSpikes.Cooldown", currentLevel);
        range = TLBMethods.getInt("ExtraAbilities.Seabarrel.EarthSpikes.Range", currentLevel);
        damage = TLBMethods.getDouble("ExtraAbilities.Seabarrel.EarthSpikes.Damage", currentLevel);
        maxHeight = TLBMethods.getInt("ExtraAbilities.Seabarrel.EarthSpikes.MaxHeight", currentLevel) + 1;
        sourceRange = TLBMethods.getInt("ExtraAbilities.Seabarrel.EarthSpikes.SourceRange", currentLevel);
        interval = TLBMethods.getLong("ExtraAbilities.Seabarrel.EarthSpikes.Interval", currentLevel);
        spikeDuration = TLBMethods.getLong("ExtraAbilities.Seabarrel.EarthSpikes.SpikeDuration", currentLevel);
        knockUp = TLBMethods.getDouble("ExtraAbilities.Seabarrel.EarthSpikes.KnockUp", currentLevel);

        if (prepare()) {
            start();
        } else {
            remove();
        }
    }

    @Override
    public void progress() {

        if (!started) {
            if (!bPlayer.getBoundAbilityName().equalsIgnoreCase(getName()) || player.getLocation().distanceSquared(getLocation()) > sourceRange * sourceRange) remove();
            return;
        }

        if (!player.isOnline() || player.isDead()) {
            remove();
        }

        if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
            removeWithCooldown();
            return;
        }

        if (time + interval <= System.currentTimeMillis()) {
            traveled++;
            time = System.currentTimeMillis();
            if (bPlayer.getBoundAbilityName().equalsIgnoreCase(getName()) && player.isSneaking()) {
                direction = player.getLocation().getDirection();
            }

            if (!stopped) {
                location = location.clone().add(new Vector(direction.getX(), 0, direction.getZ()).normalize());
                if (!(location.getBlock().getType() == Material.DRIPSTONE_BLOCK) && !(location.getBlock().getType() == Material.POINTED_DRIPSTONE)) {
                    if (!getRightLocation()) {
                        removeWithCooldown();
                        return;
                    }
                    spikes.add(new Spike(location, (int) ((double) traveled / (double) range * (double) maxHeight) + 2, this));
                    playEarthbendingSound(location);
                }
            }

            boolean ended = true;
            for (Spike spike : spikes) {
                if (!spike.update()) ended = false;
            }
            if (ended) remove();

            for (Spike spike : spikes) {
                if (spike.reachedHeight() && spike.getStartTime() + spikeDuration <= System.currentTimeMillis()) {
                    spike.setReverting(true);
                }
            }
            if (traveled >= range && !stopped) {
                removeWithCooldown();
            }
        }

    }

    public boolean prepare() {
        if (!bPlayer.canBend(this)) return false;
        Block block = getEarthSourceBlock(sourceRange);

        if (block == null) return false;
        if (!canBend(block)) return false;


        location = block.getLocation();
        sourceBlock = new TempBlock(block, Material.DRIPSTONE_BLOCK);
        return true;
    }

    public void changeSource() {
        TempBlock prevSource = sourceBlock;
        if (prepare()) {
            if ((prevSource.getLocation().getX() == sourceBlock.getLocation().getX() && prevSource.getLocation().getY() == sourceBlock.getLocation().getY() && prevSource.getLocation().getZ() == sourceBlock.getLocation().getZ())) return;
            prevSource.revertBlock();
        }
    }

    public boolean getRightLocation() {

        if (!isTransparent(location.clone().add(0, 1, 0).getBlock())) {

            if (canBend(location.clone().add(0, 1, 0).getBlock())) {
                location.add(0, 1, 0);
                return true;
            }
            return false;

        }
        if (!isTransparent(location.getBlock())) {

            if (canBend(location.getBlock())) return true;
            return false;
        }

        if (!isTransparent(location.clone().subtract(0, 1, 0).getBlock())) {

            if (canBend(location.clone().subtract(0, 1, 0).getBlock())) {
                location.subtract(0, 1, 0);
                return true;
            }
            return false;
        }

        if (!isTransparent(location.clone().subtract(0, 2, 0).getBlock())) {

            if (canBend(location.clone().subtract(0, 2, 0).getBlock())) {
                location.subtract(0, 2, 0);
                return true;
            }
            return false;

        }

        return false;
    }

    public boolean canBend(Block block) {
        if (GeneralMethods.isRegionProtectedFromBuild(player, block.getLocation())) return false;
        if (isSandbendable(block) && bPlayer.hasSubElement(Element.SAND)) return true;
        if (isLavabendable(block) && bPlayer.hasSubElement(Element.LAVA)) return true;
        if (isMetalbendable(block) && bPlayer.hasSubElement(Element.METAL)) return true;
        return isEarthbendable(block) && bPlayer.hasElement(Element.EARTH);
    }

    public void remove() {
        super.remove();
        kill();
        if (sourceBlock != null) sourceBlock.revertBlock();
    }

    public void onClick() {
        if (!started) {
            sourceBlock.revertBlock();
            spikes.add(new Spike(location, 2, this));
            started = true;
            direction = player.getLocation().getDirection();
        }
    }

    public boolean isStarted() {
        return this.started;
    }

    public boolean isStopped() {
        return this.stopped;
    }

    public void removeWithCooldown() {
        bPlayer.addCooldown(this, cooldown);
        stopped = true;
    }

    public void kill() {
        for (Spike spike : spikes) {
            spike.revertAll();
        }
    }

    public Player getPlayer() {
        return player;
    }

    public double getDamage() {
        return damage;
    }

    public double getKnockUp() {
        return this.knockUp;
    }

    public void addDamaged(Entity entity) {
        damaged.add(entity);
    }

    public ArrayList<Entity> getDamaged() {
        return this.damaged;
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public String getName() {
        return "EarthSpikes";
    }

    @Override
    public Location getLocation() {
        return location;
    }

    @Override
    public void load() {

        FileConfiguration config = ConfigManager.getConfig();
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.Cooldown", 4000);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.Range", 30);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.Damage", 4);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.MaxHeight", 5);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.SourceRange", 7);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.Interval", 60);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.SpikeDuration", 9000);
        config.addDefault("ExtraAbilities.Seabarrel.EarthSpikes.KnockUp", 0.5);
        ConfigManager.defaultConfig.save();

        ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new EarthSpikesListener(), ProjectKorra.plugin);
        ProjectKorra.plugin.getLogger().info("Successfully enabled " + getName() + " " + getVersion() + " by " + getAuthor());
    }

    @Override
    public void stop() {

    }

    @Override
    public String getAuthor() {
        return "Seabarrel";
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public String getDescription() {
        return "Shoot a line of spikes increasing height the further it goes!";
    }

    @Override
    public String getInstructions() {
        return "tab sneak on an earthbendable block, then click to shoot your spikes! \nHold shift to control the direction.";
    }
}
