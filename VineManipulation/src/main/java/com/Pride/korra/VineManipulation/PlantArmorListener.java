package com.Pride.korra.VineManipulation;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;

public class PlantArmorListener implements Listener {
   @EventHandler(
      priority = EventPriority.NORMAL
   )
   public void onPlayerDamage(EntityDamageEvent event) {
      // No longer needed - resistance effect is maintained automatically
      // The PlantArmor ability now uses resistance instead of absorption hearts
   }
}
