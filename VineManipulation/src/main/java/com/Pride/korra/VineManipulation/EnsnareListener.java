package com.Pride.korra.VineManipulation;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerAnimationEvent;

public class EnsnareListener implements Listener {
   @EventHandler
   public void onSwing(PlayerAnimationEvent event) {
      if (!event.isCancelled()) {
         if (!CoreAbility.hasAbility(event.getPlayer(), Ensnare.class)) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer != null && bPlayer.canBend(CoreAbility.getAbility("VineManipulation")) && CoreAbility.getAbility(event.getPlayer(), Ensnare.class) == null) {
               new Ensnare(event.getPlayer());
            }

         }
      }
   }

   @EventHandler
   public void onDamage(EntityDamageEvent event) {
      if (!event.isCancelled()) {
         if (event.getEntity() instanceof Player) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer((Player)event.getEntity());
            if (bPlayer != null && CoreAbility.getAbility((Player)event.getEntity(), Ensnare.class) != null) {
               Ensnare ability = (Ensnare)CoreAbility.getAbility((Player)event.getEntity(), Ensnare.class);
               ability.remove();
            }
         }

      }
   }
}
