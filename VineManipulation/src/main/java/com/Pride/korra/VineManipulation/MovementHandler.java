package com.Pride.korra.VineManipulation;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.util.ActionBar;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.scheduler.BukkitRunnable;

public class MovementHandler {
   private LivingEntity entity;
   private BukkitRunnable runnable;
   private MovementHandler.ResetTask reset = null;

   public MovementHandler(LivingEntity entity) {
      this.entity = entity;
   }

   public void stop(final long duration, final String message) {
      if (this.entity instanceof Player) {
         final long start = System.currentTimeMillis();
         final Player player = (Player)this.entity;
         player.setMetadata("movement:stop", new FixedMetadataValue(ProjectKorra.plugin, 0));
         this.runnable = new BukkitRunnable() {
            public void run() {
               ActionBar.sendActionBar(message, new Player[]{player});
               if (System.currentTimeMillis() >= start + duration / 20L * 1000L) {
                  player.removeMetadata("movement:stop", ProjectKorra.plugin);
                  MovementHandler.this.reset();
               }

            }
         };
         this.runnable.runTaskTimer(ProjectKorra.plugin, 0L, 1L);
      } else {
         this.runnable = new BukkitRunnable() {
            public void run() {
               MovementHandler.this.allowMove();
            }
         };
         (new BukkitRunnable() {
            public void run() {
               if (MovementHandler.this.entity.isOnGround()) {
                  MovementHandler.this.entity.setAI(false);
                  this.cancel();
                  MovementHandler.this.runnable.runTaskLater(ProjectKorra.plugin, duration);
               }

            }
         }).runTaskTimer(ProjectKorra.plugin, 0L, 1L);
      }

   }

   private void allowMove() {
      if (!(this.entity instanceof Player)) {
         this.entity.setAI(true);
      }

      if (this.reset != null) {
         this.reset.run();
      }

   }

   public void reset() {
      this.runnable.cancel();
      this.allowMove();
   }

   public LivingEntity getEntity() {
      return this.entity;
   }

   public void setResetTask(MovementHandler.ResetTask reset) {
      this.reset = reset;
   }

   public static boolean isStopped(Entity entity) {
      return entity.hasMetadata("movement:stop");
   }

   public interface ResetTask {
      void run();
   }
}
