package com.Pride.korra.VineManipulation;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class VineManipListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (event.isSneaking()) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer != null && bPlayer.canBend(CoreAbility.getAbility("VineManipulation")) && CoreAbility.getAbility(event.getPlayer(), VineManipulation.class) == null) {
               new VineManipulation(event.getPlayer());
            }

         }
      }
   }

   @EventHandler
   public void onHit(PlayerAnimationEvent event) {
      if (!event.isCancelled()) {
         Player player = event.getPlayer();
         BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
         VineManipulation vineManip = (VineManipulation)CoreAbility.getAbility(player, VineManipulation.class);
         if (bPlayer != null && CoreAbility.getAbility(player, VineManipulation.class) != null) {
            new PlantArmor(player);
         }

      }
   }
}
