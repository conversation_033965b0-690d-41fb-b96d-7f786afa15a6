package com.Pride.korra.FireDrive;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.PKListener;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;

public class FireDriveListener implements Listener {
   @EventHandler
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (!CoreAbility.hasAbility(event.getPlayer(), FireDrive.class)) {
            new FireDrive(event.getPlayer());
         }
      }
   }

   @EventHandler(priority = EventPriority.HIGH)
   public void onFallDamage(EntityDamageEvent event) {
      if (event.getCause() != EntityDamageEvent.DamageCause.FALL || !(event.getEntity() instanceof Player)) {
         return;
      }

      Player player = (Player) event.getEntity();

      // Check if player is in blastedPlayers list
      if (!PKListener.blastedPlayers.contains(player.getUniqueId())) {
         return;
      }

      // Get configurable ground distance
      double groundDistance = ConfigManager.getConfig().getDouble("ExtraAbilities.Prride.FireDrive.FallDamageProtection.GroundDistance");

      // Check if player is within the configured distance from the ground
      if (isWithinGroundDistance(player, groundDistance)) {
         event.setCancelled(true);
         // Remove player from blastedPlayers since they've landed safely
         PKListener.blastedPlayers.remove(player.getUniqueId());
      }
   }

   private boolean isWithinGroundDistance(Player player, double distance) {
      Location playerLoc = player.getLocation();

      // Check blocks below the player up to the specified distance
      for (int i = 1; i <= Math.ceil(distance); i++) {
         Location checkLoc = playerLoc.clone().subtract(0, i, 0);
         if (checkLoc.getBlock().getType().isSolid()) {
            return i <= distance;
         }
      }

      return false; // No solid ground found within distance
   }
}
