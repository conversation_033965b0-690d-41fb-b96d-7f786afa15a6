package com.Pride.korra.FireDrive;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.PKListener;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ActionBar;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TimeUtil;

public class FireDrive extends FireAbility implements AddonAbility {
   public static List<UUID> playerBlue = new ArrayList();
   private long cooldown;
   private long duration;
   private long time;
   private double damage;
   private double speed;
   private double blueDriveSpeed;
   private static String bindMsgBlue;
   private static String unbindMsgBlue;
   private static boolean blueFireDriveEnabled;
   private Location location;
   private long currentLevel;
   private int fallDistanceLimit;

   public FireDrive(Player player) {
      super(player);
      if (this.bPlayer.canBend(this) && this.bPlayer.canBendIgnoreBinds(this)) {
         this.setFields();
         if (this.bPlayer.isOnCooldown(getAbility("FireJet"))) {
            ActionBar.sendActionBar(ChatColor.RED + "You are too weak to use this... Wait " + TimeUtil.formatTime(this.bPlayer.getCooldown("FireJet") - System.currentTimeMillis()) + ".", new Player[]{player});
            this.remove();
         } else {
            this.start();
         }
      }
   }

   private void setFields() {
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = TLBMethods.limitLevels(this.player, statLevel);

      this.cooldown = TLBMethods.getLong("ExtraAbilities.Prride.FireDrive.Cooldown", currentLevel);
      this.duration = TLBMethods.getLong("ExtraAbilities.Prride.FireDrive.Duration", currentLevel);
      this.damage = TLBMethods.getDouble("ExtraAbilities.Prride.FireDrive.Damage", currentLevel);
      this.speed = TLBMethods.getDouble("ExtraAbilities.Prride.FireDrive.Speed", currentLevel);
      blueFireDriveEnabled = ConfigManager.getConfig().getBoolean("ExtraAbilities.Prride.FireDrive.BlueFireDrive.Enabled");
      this.blueDriveSpeed = TLBMethods.getDouble("ExtraAbilities.Prride.FireDrive.BlueFireDrive.Speed", currentLevel);
      this.fallDistanceLimit = TLBMethods.getInt("ExtraAbilities.Prride.FireDrive.FallDistanceLimit", currentLevel);
      bindMsgBlue = "Your FireDrive is now blue.";
      unbindMsgBlue = "Your FireDrive is now back to normal.";
      this.location = this.player.getLocation().clone().subtract(0.0D, 1.0D, 0.0D);
      this.time = System.currentTimeMillis();
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public Location getLocation() {
      return this.location;
   }

   public String getName() {
      return "FireDrive";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return true;
   }

   public static int getDistance(Entity e){
      Location loc = e.getLocation().clone();
      double y = loc.getBlockY();
      int distance = 0;
      for (double i = y; i >= 0; i--){
          loc.setY(i);
         if(loc.getBlock().getType().isSolid())break;
          distance++;
      }
      return distance;
  }

   public void progress() {
      if (!this.player.isDead() && this.player.isOnline()) {
         if (getDistance(player) > this.fallDistanceLimit) {
            this.remove();
         } else if (System.currentTimeMillis() > this.time + this.duration) {
            this.bPlayer.addCooldown("FireJet", this.cooldown);
            this.bPlayer.addCooldown("FireDrive", this.cooldown);
            this.remove();
         } else if (!this.bPlayer.canBendIgnoreBindsCooldowns(this)) {
            this.remove();
         } else if (!this.player.isSneaking()) {
            this.remove();
            if (System.currentTimeMillis() > this.time + 1000L) {
               this.bPlayer.addCooldown("FireJet", this.cooldown);
               this.bPlayer.addCooldown("FireDrive", this.cooldown);
            }

         } else {
            Iterator var2 = GeneralMethods.getEntitiesAroundPoint(this.location, 2.0D).iterator();

            while(var2.hasNext()) {
               Entity entity = (Entity)var2.next();
               if (!GeneralMethods.isRegionProtectedFromBuild(this.player, "FireDrive", entity.getLocation()) && entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId() && !(entity instanceof ArmorStand)) {
                  entity.setVelocity(entity.getLocation().toVector().subtract(this.player.getLocation().toVector()).multiply(1));

                  DamageHandler.damageEntity(entity, this.damage, this);
                  this.remove();
               }
            }

            Vector velocity;
            if (playerBlue.contains(this.player.getUniqueId())) {
               velocity = this.player.getEyeLocation().getDirection().clone().normalize().multiply(-this.blueDriveSpeed);
               this.player.setVelocity(velocity);
               //this.player.setFallDistance(0.0F);
            } else {
               velocity = this.player.getEyeLocation().getDirection().clone().normalize().multiply(-this.speed);
               this.player.setVelocity(velocity);
               //this.player.setFallDistance(0.0F);
               this.bPlayer.addCooldown("FireJet", this.cooldown);
               this.bPlayer.addCooldown("FireDrive", this.cooldown);

               if (!this.player.isOnGround() && !PKListener.blastedPlayers.contains(this.player.getUniqueId()) && isWithinGroundDistance(this.player)) {
                  PKListener.blastedPlayers.add(this.player.getUniqueId());
               }
            }

            this.playFirebendingParticles(GeneralMethods.getLeftSide(this.player.getLocation(), 0.55D).add(0.0D, 1.2D, 0.0D), 10, 0.10000000149011612D, 0.10000000149011612D, 0.10000000149011612D);
            this.playFirebendingParticles(GeneralMethods.getRightSide(this.player.getLocation(), 0.55D).add(0.0D, 1.2D, 0.0D), 10, 0.10000000149011612D, 0.10000000149011612D, 0.10000000149011612D);
            ParticleEffect.SMOKE.display(GeneralMethods.getLeftSide(this.player.getLocation(), 0.55D).add(0.0D, 1.2D, 0.0D), 1, 0.10000000149011612D, 0.10000000149011612D, 0.10000000149011612D, 0.0D);
            ParticleEffect.SMOKE.display(GeneralMethods.getRightSide(this.player.getLocation(), 0.55D).add(0.0D, 1.2D, 0.0D), 1, 0.10000000149011612D, 0.10000000149011612D, 0.10000000149011612D, 0.0D);
         }
      } else {
         this.remove();
      }
   }

   public String getAuthor() {
      return "Prride";
   }

   public String getVersion() {
      return "Build v1.0";
   }

   public String getDescription() {
      return "Firebenders are able to concentrate their fire into a short burst to propel themselves backwards instead of forwards! They will deal damage as they bash towards their enemy.";
   }

   public String getInstructions() {
      return "Tap sneak to propel yourself backwards.";
   }

   private boolean isWithinGroundDistance(Player player) {
      double groundDistance = ConfigManager.getConfig().getDouble("ExtraAbilities.Prride.FireDrive.FallDamageProtection.GroundDistance");
      Location playerLoc = player.getLocation();

      // Check blocks below the player up to the specified distance
      for (int i = 1; i <= Math.ceil(groundDistance); i++) {
         Location checkLoc = playerLoc.clone().subtract(0, i, 0);
         if (checkLoc.getBlock().getType().isSolid()) {
            return i <= groundDistance;
         }
      }

      return false; // No solid ground found within distance
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new FireDriveListener(), ProjectKorra.plugin);
      ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.Cooldown", 6000);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.Duration", 2100);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.Damage", 3);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.Speed", 0.9D);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.BlueFireDrive.Enabled", true);
      ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.FireDrive.BlueFireDrive.Speed", 1.1D);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
   }
}
