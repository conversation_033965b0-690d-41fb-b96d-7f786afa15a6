package com.thelastblockbender.quicksand;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.inventory.EquipmentSlot;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import com.projectkorra.projectkorra.util.ClickType;

public class QuicksandListener implements Listener {

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.isCancelled()) {
            return;
        }

        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);

        if (bPlayer == null || !bPlayer.canCurrentlyBendWithWeapons()) {
            return;
        }

        // Only handle main hand interactions
        if (event.getHand() != EquipmentSlot.HAND) {
            return;
        }

        ClickType clickType = null;

        switch (event.getAction()) {
            case LEFT_CLICK_AIR:
            case LEFT_CLICK_BLOCK:
                clickType = ClickType.LEFT_CLICK;
                break;
            case RIGHT_CLICK_AIR:
            case RIGHT_CLICK_BLOCK:
                clickType = ClickType.RIGHT_CLICK;
                break;
            default:
                return;
        }

        // Add the click to the combo manager
        ComboManager.addComboAbility(player, clickType);
    }

    @EventHandler
    public void onPlayerToggleSneak(PlayerToggleSneakEvent event) {
        if (event.isCancelled()) {
            return;
        }

        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);

        if (bPlayer == null || !bPlayer.canCurrentlyBendWithWeapons()) {
            return;
        }

        ClickType clickType = event.isSneaking() ? ClickType.SHIFT_DOWN : ClickType.SHIFT_UP;

        // Add the sneak action to the combo manager
        ComboManager.addComboAbility(player, clickType);
    }
}
