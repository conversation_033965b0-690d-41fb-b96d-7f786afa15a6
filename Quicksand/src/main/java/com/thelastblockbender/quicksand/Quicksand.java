package com.thelastblockbender.quicksand;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.BlockDisplay;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Transformation;
import org.bukkit.util.Vector;
import org.joml.AxisAngle4f;
import org.joml.Vector3f;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

public class Quicksand extends SandAbility implements AddonAbility, ComboAbility {
    
    // Configuration variables
    private long cooldown;
    private double range;
    private double radius;
    private long duration;
    private double sinkSpeed;
    private double maxSinkDepth;
    private int slownessDuration;
    private int slownessAmplifier;
    
    // Runtime variables
    private Location center;
    private Set<Location> quicksandBlocks;
    private Set<BlockDisplay> sandDisplays;
    private Map<Entity, Double> sinkingEntities;
    private Map<Location, TempBlock> cobwebBlocks;
    private long startTime;
    private long currentLevel;
    
    public Quicksand(Player player) {
        super(player);

        // Debug message
        player.sendMessage("§6[Debug] Quicksand constructor called");

        if (!bPlayer.canBendIgnoreBinds(this)) {
            player.sendMessage("§c[Debug] Cannot bend Quicksand");
            return;
        }

        setFields();

        if (prepare()) {
            player.sendMessage("§a[Debug] Quicksand created successfully!");
            start();
            bPlayer.addCooldown(this);
        } else {
            player.sendMessage("§c[Debug] Quicksand preparation failed");
        }
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);
        
        this.cooldown = TLBMethods.getLong("ExtraAbilities.TLB.Quicksand.Cooldown", this.currentLevel);
        this.range = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.Range", this.currentLevel);
        this.radius = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.Radius", this.currentLevel);
        this.duration = TLBMethods.getLong("ExtraAbilities.TLB.Quicksand.Duration", this.currentLevel);
        this.sinkSpeed = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.SinkSpeed", this.currentLevel);
        this.maxSinkDepth = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.MaxSinkDepth", this.currentLevel);
        this.slownessDuration = TLBMethods.getInt("ExtraAbilities.TLB.Quicksand.SlownessDuration", this.currentLevel);
        this.slownessAmplifier = TLBMethods.getInt("ExtraAbilities.TLB.Quicksand.SlownessAmplifier", this.currentLevel);
        
        this.quicksandBlocks = new HashSet<>();
        this.sandDisplays = new HashSet<>();
        this.sinkingEntities = new HashMap<>();
        this.cobwebBlocks = new HashMap<>();
        this.startTime = System.currentTimeMillis();
    }
    
    private boolean prepare() {
        // Find target location where player is looking
        Location targetLoc = GeneralMethods.getTargetedLocation(player, range);
        if (targetLoc == null) {
            return false;
        }
        
        // Find ground surface
        Block targetBlock = targetLoc.getBlock();
        while (targetBlock.getY() > 0 && !GeneralMethods.isSolid(targetBlock)) {
            targetBlock = targetBlock.getRelative(0, -1, 0);
        }
        
        if (!isEarthbendable(targetBlock)) {
            return false;
        }
        
        this.center = targetBlock.getLocation().add(0.5, 1, 0.5);
        
        // Check if area is protected
        if (GeneralMethods.isRegionProtectedFromBuild(player, center)) {
            return false;
        }
        
        createQuicksand();
        return true;
    }
    
    private void createQuicksand() {
        // Create circular pattern of quicksand
        for (double x = -radius; x <= radius; x += 0.5) {
            for (double z = -radius; z <= radius; z += 0.5) {
                double distance = Math.sqrt(x * x + z * z);
                if (distance <= radius) {
                    Location loc = center.clone().add(x, 0, z);
                    Block block = loc.getBlock();
                    
                    // Only affect air blocks at ground level
                    if (block.getType() == Material.AIR && GeneralMethods.isSolid(block.getRelative(0, -1, 0))) {
                        quicksandBlocks.add(loc);
                        
                        // Create cobweb for slowing effect
                        TempBlock cobweb = new TempBlock(block, Material.COBWEB);
                        cobweb.setRevertTime(duration);
                        cobwebBlocks.put(loc, cobweb);
                        
                        // Create sand display entity overlay
                        createSandDisplay(loc);
                    }
                }
            }
        }
        
        // Play sound and particles
        center.getWorld().playSound(center, Sound.BLOCK_SAND_BREAK, 1.0f, 0.8f);
        center.getWorld().spawnParticle(Particle.BLOCK, center, 20, radius, 0.1, radius, 
            Material.SAND.createBlockData());
    }
    
    private void createSandDisplay(Location loc) {
        BlockDisplay sandDisplay = loc.getWorld().spawn(loc.clone().add(0, -0.1, 0), BlockDisplay.class);
        sandDisplay.setBlock(Material.SAND.createBlockData());
        
        // Make it slightly smaller and positioned to look like it's on the ground
        Transformation transform = new Transformation(
            new Vector3f(0, 0, 0),
            new AxisAngle4f(0, 0, 0, 1),
            new Vector3f(1.0f, 0.2f, 1.0f),
            new AxisAngle4f(0, 0, 0, 1)
        );
        sandDisplay.setTransformation(transform);
        
        sandDisplays.add(sandDisplay);
        
        // Schedule removal
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            com.projectkorra.projectkorra.ProjectKorra.plugin, 
            () -> sandDisplay.remove(), 
            duration / 50
        );
    }
    
    @Override
    public void progress() {
        if (player.isDead() || !player.isOnline()) {
            remove();
            return;
        }
        
        if (System.currentTimeMillis() > startTime + duration) {
            remove();
            return;
        }
        
        // Check for entities in quicksand
        for (Location loc : quicksandBlocks) {
            for (Entity entity : GeneralMethods.getEntitiesAroundPoint(loc, 0.8)) {
                if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId()) {
                    handleEntityInQuicksand((LivingEntity) entity, loc);
                }
            }
        }
        
        // Update sinking entities
        updateSinkingEntities();
        
        // Spawn particles occasionally
        if (System.currentTimeMillis() % 1000 < 50) {
            center.getWorld().spawnParticle(Particle.BLOCK_CRUMBLE, center, 5, radius * 0.5, 0.1, radius * 0.5, 
                Material.SAND.createBlockData());
        }
    }
    
    private void handleEntityInQuicksand(LivingEntity entity, Location quicksandLoc) {
        if (!sinkingEntities.containsKey(entity)) {
            sinkingEntities.put(entity, 0.0);
            
            // Apply slowness effect
            entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 
                slownessDuration, slownessAmplifier, false, false));
        }
        
        // Prevent entity from jumping/moving up easily
        Vector velocity = entity.getVelocity();
        if (velocity.getY() > 0) {
            velocity.setY(velocity.getY() * 0.3);
            entity.setVelocity(velocity);
        }
    }
    
    private void updateSinkingEntities() {
        Set<Entity> toRemove = new HashSet<>();
        
        for (Map.Entry<Entity, Double> entry : sinkingEntities.entrySet()) {
            Entity entity = entry.getKey();
            double currentSink = entry.getValue();
            
            if (entity.isDead() || !entity.isValid()) {
                toRemove.add(entity);
                continue;
            }
            
            // Check if entity is still in quicksand area
            boolean inQuicksand = false;
            for (Location loc : quicksandBlocks) {
                if (entity.getLocation().distance(loc) <= 1.0) {
                    inQuicksand = true;
                    break;
                }
            }
            
            if (!inQuicksand) {
                toRemove.add(entity);
                continue;
            }
            
            // Sink the entity gradually
            if (currentSink < maxSinkDepth) {
                double newSink = currentSink + sinkSpeed;
                if (newSink > maxSinkDepth) {
                    newSink = maxSinkDepth;
                }
                
                Location newLoc = entity.getLocation().clone();
                newLoc.setY(newLoc.getY() - (newSink - currentSink));
                entity.teleport(newLoc);
                
                sinkingEntities.put(entity, newSink);
                
                // Spawn sinking particles
                entity.getWorld().spawnParticle(Particle.BLOCK, entity.getLocation().add(0, 1, 0), 
                    3, 0.3, 0.3, 0.3, Material.SAND.createBlockData());
            }
        }
        
        // Remove entities that are no longer sinking
        for (Entity entity : toRemove) {
            sinkingEntities.remove(entity);
        }
    }
    
    @Override
    public void remove() {
        super.remove();
        
        // Clean up display entities
        for (BlockDisplay display : sandDisplays) {
            if (display != null && display.isValid()) {
                display.remove();
            }
        }
        
        // Clean up temp blocks
        for (TempBlock tempBlock : cobwebBlocks.values()) {
            if (tempBlock != null) {
                tempBlock.revertBlock();
            }
        }
        
        // Reset sinking entities
        for (Entity entity : sinkingEntities.keySet()) {
            if (entity instanceof LivingEntity) {
                ((LivingEntity) entity).removePotionEffect(PotionEffectType.SLOWNESS);
            }
        }
        
        sandDisplays.clear();
        quicksandBlocks.clear();
        sinkingEntities.clear();
        cobwebBlocks.clear();
    }

    // Required ComboAbility methods
    @Override
    public ArrayList<AbilityInformation> getCombination() {
        ArrayList<AbilityInformation> combo = new ArrayList<>();
        combo.add(new AbilityInformation("Shockwave", ClickType.SHIFT_DOWN));
        combo.add(new AbilityInformation("Shockwave", ClickType.SHIFT_UP));
        combo.add(new AbilityInformation("Crumble", ClickType.RIGHT_CLICK));
        return combo;
    }

    @Override
    public Object createNewComboInstance(Player player) {
        return new Quicksand(player);
    }

    @Override
    public String getInstructions() {
        return "Shockwave (Tap Sneak) > Crumble (Right Click)";
    }

    // Required Ability methods
    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public Location getLocation() {
        return center;
    }

    @Override
    public String getName() {
        return "Quicksand";
    }

    @Override
    public String getDescription()  {
        return "Create a treacherous patch of quicksand that slows and gradually sinks enemies who step on it.";
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public boolean isSneakAbility() {
        return false;
    }

    @Override
    public boolean isHiddenAbility() {
        return false;
    }

    @Override
    public void load() {
        // Add default configuration values
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Cooldown", 15000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Range", 20.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Radius", 3.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Duration", 10000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SinkSpeed", 0.05);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.MaxSinkDepth", 1.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SlownessDuration", 100);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SlownessAmplifier", 2);

        ConfigManager.defaultConfig.save();
        com.projectkorra.projectkorra.ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility) this);
        com.projectkorra.projectkorra.ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
        com.projectkorra.projectkorra.ProjectKorra.log.info("Quicksand combo sequence: Shockwave (Tap Sneak) > Crumble (Right Click)");
    }

    @Override
    public void stop() {
        com.projectkorra.projectkorra.ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
}
