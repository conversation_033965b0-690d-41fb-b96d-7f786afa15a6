package com.thelastblockbender.quicksand;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.entity.BlockDisplay;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Transformation;
import org.bukkit.util.Vector;
import org.joml.AxisAngle4f;
import org.joml.Vector3f;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;

public class Quicksand extends SandAbility implements AddonAbility, ComboAbility {

    // Material sets for determining display block type
    private static final Set<Material> SAND_MATERIALS = EnumSet.of(
        Material.SAND, Material.RED_SAND, Material.SANDSTONE, Material.RED_SANDSTONE,
        Material.SMOOTH_SANDSTONE, Material.SMOOTH_RED_SANDSTONE, Material.CUT_SANDSTONE,
        Material.CUT_RED_SANDSTONE, Material.CHISELED_SANDSTONE, Material.CHISELED_RED_SANDSTONE
    );

    private static final Set<Material> DIRT_MATERIALS = EnumSet.of(
        Material.DIRT, Material.GRASS_BLOCK, Material.COARSE_DIRT, Material.PODZOL,
        Material.MYCELIUM, Material.DIRT_PATH, Material.FARMLAND, Material.ROOTED_DIRT
    );

    // Configuration variables
    private long cooldown;
    private double range;
    private double radius;
    private long duration;
    private double sinkSpeed;
    private double maxSinkDepth;
    private int slownessDuration;
    private int slownessAmplifier;
    private long spawnDelay;
    
    // Runtime variables
    private Location center;
    private Set<Location> quicksandBlocks;
    private Set<BlockDisplay> sandDisplays;
    private Map<Entity, Double> sinkingEntities;
    private Map<Location, TempBlock> cobwebBlocks;
    private Map<Location, Material> originalBlockMaterials;
    private long startTime;
    private long currentLevel;
    private boolean quicksandSpawned;
    private long spawnTime;
    
    public Quicksand(Player player) {
        super(player);

        // Debug message
        player.sendMessage("§6[Debug] Quicksand constructor called");

        if (!bPlayer.canBendIgnoreBinds(this)) {
            player.sendMessage("§c[Debug] Cannot bend Quicksand");
            return;
        }

        setFields();

        if (prepare()) {
            player.sendMessage("§a[Debug] Quicksand created successfully!");
            start();
            bPlayer.addCooldown(this);
        } else {
            player.sendMessage("§c[Debug] Quicksand preparation failed");
        }
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);
        
        this.cooldown = TLBMethods.getLong("ExtraAbilities.TLB.Quicksand.Cooldown", this.currentLevel);
        this.range = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.Range", this.currentLevel);
        this.radius = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.Radius", this.currentLevel);
        this.duration = TLBMethods.getLong("ExtraAbilities.TLB.Quicksand.Duration", this.currentLevel);
        this.sinkSpeed = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.SinkSpeed", this.currentLevel);
        this.maxSinkDepth = TLBMethods.getDouble("ExtraAbilities.TLB.Quicksand.MaxSinkDepth", this.currentLevel);
        this.slownessDuration = TLBMethods.getInt("ExtraAbilities.TLB.Quicksand.SlownessDuration", this.currentLevel);
        this.slownessAmplifier = TLBMethods.getInt("ExtraAbilities.TLB.Quicksand.SlownessAmplifier", this.currentLevel);
        this.spawnDelay = TLBMethods.getLong("ExtraAbilities.TLB.Quicksand.SpawnDelay", this.currentLevel);

        this.quicksandBlocks = new HashSet<>();
        this.sandDisplays = new HashSet<>();
        this.sinkingEntities = new HashMap<>();
        this.cobwebBlocks = new HashMap<>();
        this.originalBlockMaterials = new HashMap<>();
        this.startTime = System.currentTimeMillis();
        this.quicksandSpawned = false;
        this.spawnTime = this.startTime + this.spawnDelay;
    }
    
    private boolean prepare() {
        // Find target location where player is looking
        Location targetLoc = GeneralMethods.getTargetedLocation(player, range);
        if (targetLoc == null) {
            return false;
        }
        
        // Find ground surface
        Block targetBlock = targetLoc.getBlock();
        while (targetBlock.getY() > 0 && !GeneralMethods.isSolid(targetBlock)) {
            targetBlock = targetBlock.getRelative(0, -1, 0);
        }
        
        if (!isEarthbendable(targetBlock)) {
            return false;
        }
        
        this.center = targetBlock.getLocation().add(0.5, 1, 0.5);

        // Check if area is protected
        if (GeneralMethods.isRegionProtectedFromBuild(player, center)) {
            return false;
        }

        // Don't create quicksand immediately - wait for delay
        return true;
    }
    
    private void createQuicksand() {
        // Create circular pattern of quicksand
        for (double x = -radius; x <= radius; x += 0.5) {
            for (double z = -radius; z <= radius; z += 0.5) {
                double distance = Math.sqrt(x * x + z * z);
                if (distance <= radius) {
                    Location loc = center.clone().add(x, -1, z); // Place at ground level
                    Block block = loc.getBlock();

                    // Replace the top layer of solid ground with cobwebs
                    if (GeneralMethods.isSolid(block) && isEarthbendable(block)) {
                        quicksandBlocks.add(loc);

                        // Store the original block material
                        originalBlockMaterials.put(loc, block.getType());

                        // Create cobweb for slowing effect at ground level
                        TempBlock cobweb = new TempBlock(block, Material.COBWEB);
                        cobweb.setRevertTime(duration);
                        cobwebBlocks.put(loc, cobweb);

                        // Create sand display entity overlay at ground level
                        createSandDisplay(loc, block.getType());
                    }
                }
            }
        }

        // Play sound and particles
        center.getWorld().playSound(center, Sound.BLOCK_SAND_BREAK, 1.0f, 0.8f);
        center.getWorld().spawnParticle(Particle.BLOCK, center, 20, radius, 0.1, radius,
            Material.SAND.createBlockData());
    }
    
    private void createSandDisplay(Location loc, Material originalMaterial) {
        // Determine the appropriate display material based on the original block
        Material displayMaterial = getDisplayMaterial(originalMaterial);

        // Spawn the sand display at the top of the cobweb block
        Location displayLoc = loc.clone().add(0, 1, 0); // Move up by 1 block to align with top
        BlockDisplay sandDisplay = displayLoc.getWorld().spawn(displayLoc, BlockDisplay.class);
        sandDisplay.setBlock(displayMaterial.createBlockData());

        // Make it look like sand/mud on the ground surface, positioned at the top
        Transformation transform = new Transformation(
            new Vector3f(0, 0.1f, 0), // Move down slightly to align with top of cobweb
            new AxisAngle4f(0, 0, 0, 1),
            new Vector3f(1.0f, 0.3f, 1.0f), // Thin layer on top
            new AxisAngle4f(0, 0, 0, 1)
        );
        sandDisplay.setTransformation(transform);

        sandDisplays.add(sandDisplay);

        // Schedule removal
        org.bukkit.Bukkit.getScheduler().runTaskLater(
            com.projectkorra.projectkorra.ProjectKorra.plugin,
            () -> sandDisplay.remove(),
            duration / 50
        );
    }

    private Material getDisplayMaterial(Material originalMaterial) {
        if (SAND_MATERIALS.contains(originalMaterial)) {
            // Use sand for sand-based blocks
            return originalMaterial == Material.RED_SAND ? Material.RED_SAND : Material.SAND;
        } else if (DIRT_MATERIALS.contains(originalMaterial)) {
            // Use mud for dirt/grass-based blocks
            return Material.MUD;
        } else {
            // Default to sand for other earth materials
            return Material.SUSPICIOUS_SAND;
        }
    }
    
    @Override
    public void progress() {
        if (player.isDead() || !player.isOnline()) {
            remove();
            return;
        }

        if (System.currentTimeMillis() > startTime + duration) {
            remove();
            return;
        }

        // Check if it's time to spawn quicksand
        if (!quicksandSpawned && System.currentTimeMillis() >= spawnTime) {
            createQuicksand();
            quicksandSpawned = true;
        }

        // Show preparation particles before quicksand spawns
        if (!quicksandSpawned) {
            if (System.currentTimeMillis() % 200 < 50) { // Every 200ms
                spawnPreparationParticles();
            }
            return; // Don't do anything else until quicksand is spawned
        }

        // Check for entities in quicksand
        for (Location loc : quicksandBlocks) {
            for (Entity entity : GeneralMethods.getEntitiesAroundPoint(loc, 0.8)) {
                if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId()) {
                    handleEntityInQuicksand((LivingEntity) entity, loc);
                }
            }
        }

        // Update sinking entities
        updateSinkingEntities();

        // Spawn particles occasionally
        if (System.currentTimeMillis() % 1000 < 50) {
            center.getWorld().spawnParticle(Particle.BLOCK_CRUMBLE, center, 5, radius * 0.5, 0.1, radius * 0.5,
                Material.SAND.createBlockData());
        }
    }

    private void spawnPreparationParticles() {
        // Create circular pattern of preparation particles
        for (double x = -radius; x <= radius; x += 1.0) {
            for (double z = -radius; z <= radius; z += 1.0) {
                double distance = Math.sqrt(x * x + z * z);
                if (distance <= radius) {
                    Location particleLoc = center.clone().add(x, -0.5, z);
                    center.getWorld().spawnParticle(Particle.BLOCK_CRUMBLE, particleLoc, 2, 0.2, 0.1, 0.2,
                        Material.SAND.createBlockData());
                }
            }
        }
    }

    private void handleEntityInQuicksand(LivingEntity entity, Location quicksandLoc) {
        if (!sinkingEntities.containsKey(entity)) {
            sinkingEntities.put(entity, 0.0);
            
            // Apply slowness effect
            entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 
                slownessDuration, slownessAmplifier, false, false));
        }
        
        // Prevent entity from jumping/moving up easily
        Vector velocity = entity.getVelocity();
        if (velocity.getY() > 0) {
            velocity.setY(velocity.getY() * 0.3);
            entity.setVelocity(velocity);
        }
    }
    
    private void updateSinkingEntities() {
        Set<Entity> toRemove = new HashSet<>();
        
        for (Map.Entry<Entity, Double> entry : sinkingEntities.entrySet()) {
            Entity entity = entry.getKey();
            double currentSink = entry.getValue();
            
            if (entity.isDead() || !entity.isValid()) {
                toRemove.add(entity);
                continue;
            }
            
            // Check if entity is still in quicksand area
            boolean inQuicksand = false;
            for (Location loc : quicksandBlocks) {
                if (entity.getLocation().distance(loc) <= 1.0) {
                    inQuicksand = true;
                    break;
                }
            }
            
            if (!inQuicksand) {
                toRemove.add(entity);
                continue;
            }
            
            // Sink the entity gradually
            if (currentSink < maxSinkDepth) {
                double newSink = currentSink + sinkSpeed;
                if (newSink > maxSinkDepth) {
                    newSink = maxSinkDepth;
                }
                
                Location newLoc = entity.getLocation().clone();
                newLoc.setY(newLoc.getY() - (newSink - currentSink));
                entity.teleport(newLoc);
                
                sinkingEntities.put(entity, newSink);
                
                // Spawn sinking particles
                entity.getWorld().spawnParticle(Particle.BLOCK, entity.getLocation().add(0, 1, 0), 
                    3, 0.3, 0.3, 0.3, Material.SAND.createBlockData());
            }
        }
        
        // Remove entities that are no longer sinking
        for (Entity entity : toRemove) {
            sinkingEntities.remove(entity);
        }
    }
    
    @Override
    public void remove() {
        super.remove();
        
        // Clean up display entities
        for (BlockDisplay display : sandDisplays) {
            if (display != null && display.isValid()) {
                display.remove();
            }
        }
        
        // Clean up temp blocks
        for (TempBlock tempBlock : cobwebBlocks.values()) {
            if (tempBlock != null) {
                tempBlock.revertBlock();
            }
        }
        
        // Reset sinking entities
        for (Entity entity : sinkingEntities.keySet()) {
            if (entity instanceof LivingEntity) {
                ((LivingEntity) entity).removePotionEffect(PotionEffectType.SLOWNESS);
            }
        }
        
        sandDisplays.clear();
        quicksandBlocks.clear();
        sinkingEntities.clear();
        cobwebBlocks.clear();
        originalBlockMaterials.clear();
    }

    // Required ComboAbility methods
    @Override
    public ArrayList<AbilityInformation> getCombination() {
        ArrayList<AbilityInformation> combo = new ArrayList<>();
        combo.add(new AbilityInformation("EarthBlast", ClickType.SHIFT_DOWN));
        combo.add(new AbilityInformation("EarthBlast", ClickType.SHIFT_UP));
        return combo;
    }

    @Override
    public Object createNewComboInstance(Player player) {
        return new Quicksand(player);
    }

    @Override
    public String getInstructions() {
        return "EarthBlast (Tap Sneak) > Crumble (Right Click)";
    }

    // Required Ability methods
    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public Location getLocation() {
        return center;
    }

    @Override
    public String getName() {
        return "Quicksand";
    }

    @Override
    public String getDescription()  {
        return "Create a treacherous patch of quicksand that slows and gradually sinks enemies who step on it.";
    }

    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public boolean isSneakAbility() {
        return false;
    }

    @Override
    public boolean isHiddenAbility() {
        return false;
    }

    @Override
    public void load() {
        // Add default configuration values
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Cooldown", 15000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Range", 20.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Radius", 3.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.Duration", 10000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SinkSpeed", 0.05);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.MaxSinkDepth", 1.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SlownessDuration", 100);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SlownessAmplifier", 2);
        ConfigManager.getConfig().addDefault("ExtraAbilities.TLB.Quicksand.SpawnDelay", 2000);

        ConfigManager.defaultConfig.save();
        com.projectkorra.projectkorra.ProjectKorra.getCollisionInitializer().addComboAbility((CoreAbility) this);
        com.projectkorra.projectkorra.ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
        com.projectkorra.projectkorra.ProjectKorra.log.info("Quicksand combo sequence: Shockwave (Tap Sneak) > Crumble (Right Click)");
    }

    @Override
    public void stop() {
        com.projectkorra.projectkorra.ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
}
