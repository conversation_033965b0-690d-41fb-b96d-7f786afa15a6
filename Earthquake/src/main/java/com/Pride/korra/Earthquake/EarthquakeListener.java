package com.Pride.korra.Earthquake;

import java.util.Iterator;
import org.bukkit.entity.FallingBlock;
import org.bukkit.entity.EntityType;
import org.bukkit.event.entity.EntityChangeBlockEvent;
import org.bukkit.event.EventHandler;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.player.PlayerToggleSneakEvent;
import org.bukkit.event.Listener;

public class EarthquakeListener implements Listener
{
    @EventHandler
    public void onSneak(final PlayerToggleSneakEvent event) {
        if (event.isCancelled()) {
            return;
        }
        if (!event.isSneaking()) {
            return;
        }
        final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
        if (bPlayer != null && bPlayer.canBend(CoreAbility.getAbility("Earthquake")) && CoreAbility.getAbility(event.getPlayer(), (Class)Earthquake.class) == null) {
            new Earthquake(event.getPlayer());
        }
    }
    
    @EventHandler
    public void removeBlocks(final EntityChangeBlockEvent event) {
        if (event.isCancelled()) {
            return;
        }
        if (event.getEntityType() == EntityType.FALLING_BLOCK) {
            for (final FallingBlock earthquakeBlocks : Earthquake.fblock) {
                if (event.getEntity() == earthquakeBlocks) {
                    event.setCancelled(true);
                }
            }
        }
    }
}
