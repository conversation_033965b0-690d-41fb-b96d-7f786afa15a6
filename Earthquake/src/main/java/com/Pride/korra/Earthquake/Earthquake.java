package com.Pride.korra.Earthquake;

import org.bukkit.plugin.Plugin;
import org.bukkit.event.Listener;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.Element;
import java.util.Iterator;
import com.projectkorra.projectkorra.util.DamageHandler;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.block.Block;
import org.bukkit.ChatColor;
import org.bukkit.Sound;
import org.bukkit.configuration.file.FileConfiguration;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import org.bukkit.block.BlockFace;
import org.bukkit.Material;
import org.bukkit.Location;
import java.util.ArrayList;
import org.bukkit.entity.FallingBlock;
import org.bukkit.util.Vector;
import java.util.Random;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;

public class Earthquake extends EarthAbility implements AddonAbility
{
    private static String path;
    private long cooldown;
    private String radiusNum;
    private double maxRadius;
    private double radius;
    private double minRadius;
    Random rand;
    private Vector direction;
    private FallingBlock fb;
    public static ArrayList<FallingBlock> fblock;
    public ArrayList<FallingBlock> fallingblock;
    private Location location;
    private double time;
    private double duration;
    private double damage;
    private double blockDamage;
    private boolean spewDirt;
    private boolean spewParticles;
    private Material groundMat;
    private boolean spewStone;
    private boolean spewSand;
    private BlockFace[] faces;
    private long currentLevel;
    
    static {
        Earthquake.path = "ExtraAbilities.Prride.Earthquake.";
        Earthquake.fblock = new ArrayList<FallingBlock>();
    }
    
    public Earthquake(final Player player) {
        super(player);
        this.rand = new Random();
        this.fallingblock = new ArrayList<FallingBlock>();
        this.faces = new BlockFace[] { BlockFace.SELF, BlockFace.EAST, BlockFace.NORTH, BlockFace.SOUTH, BlockFace.WEST, BlockFace.NORTH_EAST, BlockFace.SOUTH_EAST, BlockFace.SOUTH_WEST, BlockFace.NORTH_WEST };
        if (!this.bPlayer.canBend((CoreAbility)this)) {
            return;
        }
        if (this.bPlayer.isOnCooldown((Ability)this)) {
            this.remove();
            return;
        }
        if (!Earthquake.fblock.isEmpty()) {
            Earthquake.fblock.clear();
        }
        if (!this.fallingblock.isEmpty()) {
            this.fallingblock.clear();
        }
        this.setFields();
        this.modify();
        this.start();
    }
    
    private void modify() {
        final int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        this.currentLevel = GeneralMethods.limitLevels(this.player, statLevel);
        this.maxRadius = (int)(this.currentLevel * 0.12 + 12.0);
        this.cooldown = 15000L - this.currentLevel * 500L;
        this.duration = (int)(this.currentLevel * 0.3 + 2.0);
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);

        final FileConfiguration config = ConfigManager.getConfig();
        this.cooldown = TLBMethods.getLong(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "Cooldown", currentLevel);
        this.maxRadius = TLBMethods.getInt(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MaxRadius", currentLevel);
        this.duration = TLBMethods.getInt(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MaxDurationInSeconds", currentLevel);
        this.blockDamage = TLBMethods.getDouble(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "BlockDamage", currentLevel);
        this.damage = TLBMethods.getDouble(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "Damage", currentLevel);
        this.minRadius = TLBMethods.getInt(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MinRadius", currentLevel);
        this.spewDirt = config.getBoolean(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewDirt");
        this.spewStone = config.getBoolean(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewStone");
        this.spewSand = config.getBoolean(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewSand");
        this.spewParticles = config.getBoolean(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewParticles");
    }
    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public Location getLocation() {
        return null;
    }
    
    public String getName() {
        return "Earthquake";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public void progress() {
        if (!bPlayer.canBendIgnoreBinds(this)) {
            this.remove();
            return;
        }
        if (this.player.isSneaking()) {
            if (this.radius <= this.maxRadius) {
                if (this.bPlayer.isOnCooldown((Ability)this)) {
                    this.remove();
                    return;
                }
                this.getRadius();
                if (this.rand.nextInt(2) == 0) {
                    this.player.getWorld().playSound(this.location, Sound.ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR, 1.0f, 0.5f);
                }
            }
        }
        else {
            if (this.rand.nextInt(2) == 0) {
                this.player.getWorld().playSound(this.location, Sound.ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR, 1.0f, 0.5f);
            }
            this.bPlayer.addCooldown((Ability)this);
            if (this.radius < this.minRadius) {
                this.remove();
                return;
            }
            this.earthQuake();
            this.damage();
        }
    }
    
    private static boolean earthbendableBlocks(final Material material) {
        return material == Material.STONE || material == Material.COBBLESTONE || material == Material.GRASS_BLOCK || material == Material.GRAVEL || material == Material.SAND || material == Material.SANDSTONE;
    }
    
    private void getRadius() {
        this.radius += 0.1;
        this.radiusNum = Integer.toString((int)this.radius);
        this.player.sendTitle(new StringBuilder().append(ChatColor.GREEN).append(ChatColor.BOLD).append(this.radiusNum).toString(), "", 1, 80, 1);
    }
    
    private void earthQuake() {
        this.time += 0.05;
        final Location playerLoc = this.player.getLocation();
        final Location loc = playerLoc.clone();
        loc.add((double)((this.rand.nextBoolean() ? 1 : -1) * this.rand.nextInt((int)this.radius)), (double)((this.rand.nextBoolean() ? 1 : -1) * this.rand.nextInt((int)this.radius)), (double)((this.rand.nextBoolean() ? 1 : -1) * this.rand.nextInt((int)this.radius)));
        final Location currLoc = loc.clone();
        final Block block = currLoc.add(0.0, 0.0, 0.0).getBlock();
        final Block block2 = currLoc.add(0.0, -2.0, 0.0).getBlock();
        final Block block3 = currLoc.add(0.0, 1.0, 0.0).getBlock();
        final Block block4 = currLoc.add(0.0, 3.0, 0.0).getBlock();
        final Block[] grassBlocks = { block2, block3, block4 };
        final Block[] stoneBlocks = { block };
        if (this.rand.nextInt(3) == 0) {
            playEarthbendingSound(currLoc);
        }
        if (this.time >= this.duration) {
            this.remove();
            return;
        }
        if (this.time <= 0.05) {
            this.player.sendTitle(new StringBuilder().append(ChatColor.GREEN).append(ChatColor.BOLD).append(this.radiusNum).toString(), ChatColor.GREEN + "Magnitude: " + this.radiusNum + "!");
            this.player.getWorld().playSound(this.location, Sound.ENTITY_ZOMBIE_ATTACK_WOODEN_DOOR, 1.0f, 0.5f);
        }
        this.location = currLoc;
        final Material groundBlock = this.player.getLocation().getBlock().getRelative(BlockFace.DOWN).getType();
        if ((this.groundMat = groundBlock) == Material.GRASS_BLOCK) {
            BlockFace[] faces;
            for (int length = (faces = this.faces).length, i = 0; i < length; ++i) {
                final BlockFace face = faces[i];
                Block[] array;
                for (int length2 = (array = grassBlocks).length, j = 0; j < length2; ++j) {
                    final Block landBlocks = array[j];
                    if (earthbendableBlocks(landBlocks.getType()) && landBlocks.getType() != Material.STONE && landBlocks.getType() != Material.COBBLESTONE && landBlocks.getType() != Material.GRAVEL && currLoc.getBlock().getRelative(face).getType() != Material.AIR && currLoc.getBlock().getRelative(face).getType() != Material.DIRT) {
                        ParticleEffect.BLOCK_CRACK.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)groundBlock.createBlockData());
                        ParticleEffect.FALLING_DUST.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.05000000074505806, (Object)Material.DIRT.createBlockData());
                        for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(currLoc, 3.0)) {
                            if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
                                DamageHandler.damageEntity(entity, this.damage, (Ability)this);
                                entity.setVelocity(currLoc.getDirection().clone().normalize().multiply(0.9));
                            }
                        }
                        if (this.spewDirt) {
                            this.spewBlocks();
                        }
                    }
                }
            }
        }
        if (groundBlock == Material.STONE || groundBlock == Material.COBBLESTONE) {
            BlockFace[] faces2;
            for (int length3 = (faces2 = this.faces).length, k = 0; k < length3; ++k) {
                final BlockFace face = faces2[k];
                Block[] array2;
                for (int length4 = (array2 = stoneBlocks).length, l = 0; l < length4; ++l) {
                    final Block landBlocks = array2[l];
                    if (earthbendableBlocks(landBlocks.getType()) && landBlocks.getType() != Material.GRASS_BLOCK && currLoc.getBlock().getRelative(face).getType() != Material.AIR && currLoc.getBlock().getRelative(face).getType() != Material.DIRT) {
                        ParticleEffect.BLOCK_CRACK.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)groundBlock.createBlockData());
                        ParticleEffect.FALLING_DUST.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.05000000074505806, (Object)groundBlock.createBlockData());
                        for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(currLoc, 3.0)) {
                            if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
                                DamageHandler.damageEntity(entity, this.damage, (Ability)this);
                                entity.setVelocity(currLoc.getDirection().clone().normalize().multiply(0.9));
                            }
                        }
                        if (this.spewStone) {
                            this.spewBlocks();
                        }
                    }
                }
            }
        }
        if (groundBlock == Material.SAND || groundBlock == Material.SANDSTONE) {
            BlockFace[] faces3;
            for (int length5 = (faces3 = this.faces).length, n = 0; n < length5; ++n) {
                final BlockFace face = faces3[n];
                Block[] array3;
                for (int length6 = (array3 = stoneBlocks).length, n2 = 0; n2 < length6; ++n2) {
                    final Block landBlocks = array3[n2];
                    if (earthbendableBlocks(landBlocks.getType()) && landBlocks.getType() != Material.GRASS_BLOCK && landBlocks.getType() != Material.STONE && landBlocks.getType() != Material.COBBLESTONE && landBlocks.getType() != Material.GRAVEL && landBlocks.getType() != Material.DIRT && currLoc.getBlock().getRelative(face).getType() != Material.AIR && currLoc.getBlock().getRelative(face).getType() != Material.DIRT) {
                        ParticleEffect.BLOCK_CRACK.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)groundBlock.createBlockData());
                        ParticleEffect.FALLING_DUST.display(currLoc, 5, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.05000000074505806, (Object)groundBlock.createBlockData());
                        for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(currLoc, 3.0)) {
                            if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
                                DamageHandler.damageEntity(entity, this.damage, (Ability)this);
                                entity.setVelocity(currLoc.getDirection().clone().normalize().multiply(0.9));
                            }
                        }
                        if (this.spewSand) {
                            this.spewBlocks();
                        }
                    }
                }
            }
        }
    }
    
    private void spewBlocks() {
        for (int i = 0; i < 1; ++i) {
            this.direction = this.player.getEyeLocation().getDirection();
            double x = this.rand.nextDouble() * 3.0;
            double z = this.rand.nextDouble() * 3.0;
            double y = this.rand.nextDouble() * 3.0;
            x = (this.rand.nextBoolean() ? x : (-x));
            z = (this.rand.nextBoolean() ? z : (-z));
            y = (this.rand.nextBoolean() ? y : (-y));
            if (this.groundMat == Material.GRASS_BLOCK) {
                this.fb = this.player.getWorld().spawnFallingBlock(this.location, Material.DIRT, (byte)0);
            }
            if (this.groundMat == Material.STONE) {
                this.fb = this.player.getWorld().spawnFallingBlock(this.location, Material.COBBLESTONE, (byte)0);
            }
            if (this.groundMat == Material.COBBLESTONE) {
                this.fb = this.player.getWorld().spawnFallingBlock(this.location, Material.STONE, (byte)0);
            }
            if (this.groundMat == Material.SAND) {
                this.fb = this.player.getWorld().spawnFallingBlock(this.location, Material.SANDSTONE, (byte)0);
            }
            if (this.groundMat == Material.SANDSTONE) {
                this.fb = this.player.getWorld().spawnFallingBlock(this.location, Material.SAND, (byte)0);
            }
            this.fb.setVelocity(this.direction.clone().add(new Vector(x, y, z)).normalize().multiply(0.9));
            this.fb.canHurtEntities();
            this.fb.setDropItem(false);
            Earthquake.fblock.add(this.fb);
            this.fallingblock.add(this.fb);
        }
    }
    
    private void damage() {
        for (final FallingBlock fblocks : this.fallingblock) {
            for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(fblocks.getLocation(), 2.5)) {
                if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
                    DamageHandler.damageEntity(entity, this.blockDamage, (Ability)this);
                }
            }
            if (this.spewParticles) {
                if (this.groundMat == Material.DIRT || this.groundMat == Material.GRASS_BLOCK) {
                    ParticleEffect.BLOCK_CRACK.display(fblocks.getLocation(), 1, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)Material.DIRT.createBlockData());
                }
                else if (this.groundMat == Material.SAND) {
                    ParticleEffect.BLOCK_CRACK.display(fblocks.getLocation(), 1, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)Material.SAND.createBlockData());
                }
                else {
                    if (this.groundMat != Material.COBBLESTONE && this.groundMat != Material.STONE) {
                        continue;
                    }
                    ParticleEffect.BLOCK_CRACK.display(fblocks.getLocation(), 1, (double)(float)Math.random(), (double)(float)Math.random(), (double)(float)Math.random(), 0.10000000149011612, (Object)Material.STONE.createBlockData());
                }
            }
        }
    }
    
    public String getDescription() {
        return String.valueOf(String.valueOf(String.valueOf(this.getVersion()))) + " created by " + this.getAuthor() + ". " + ChatColor.BOLD + "\n'Cause you make my earth quake. Ohh, you make my earth quake." + "\nRiding around, your love is shakin' me up and it's making my heart break." + ChatColor.RESET + Element.EARTH.getColor() + "\nEarthquake is an advanced earth ability that allows the user to harness the full magnitude of the powers from the Earth's crust. " + "Any targets around a certain radius of the player's location will be damaged by the earth or by the rocks that are " + "thrown around by the earthquake.";
    }
    
    public String getInstructions() {
        return Element.CHI.getColor() + "Hold sneak to charge and release to quake the earth. (The larger the radius, the less frequent" + "the quakes and spews. The smaller, the more frequent and vice versa)";
    }
    
    public String getAuthor() {
        return "Prride";
    }
    
    public String getVersion() {
        return "Earthquake V1.5";
    }
    
    public void load() {
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new EarthquakeListener(), (Plugin)ProjectKorra.plugin);
        ProjectKorra.log.info(String.valueOf(String.valueOf(String.valueOf(this.getName()))) + " by " + this.getAuthor() + " " + this.getVersion() + " loaded!");
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "Cooldown", (Object)10000);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MaxRadius", (Object)24);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MinRadius", (Object)1);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "MaxDurationInSeconds", (Object)5);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "Damage", (Object)3);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "BlockDamage", (Object)2);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewDirt", (Object)true);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewStone", (Object)false);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewSand", (Object)false);
        ConfigManager.getConfig().addDefault(String.valueOf(String.valueOf(String.valueOf(Earthquake.path))) + "SpewParticles", (Object)false);
        ConfigManager.defaultConfig.save();
    }
    
    public void stop() {
    }
}
