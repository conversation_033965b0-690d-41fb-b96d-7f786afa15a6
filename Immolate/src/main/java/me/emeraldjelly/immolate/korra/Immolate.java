package me.emeraldjelly.immolate.korra;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.CombustionAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import java.util.Iterator;
import java.util.Random;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.permissions.Permission;
import org.bukkit.permissions.PermissionDefault;
import org.bukkit.util.Vector;

public class Immolate extends CombustionAbility implements AddonAbility {
   private Permission perm;
   private Location pulse;
   private Location origin;
   private Vector direct;
   private long cooldown;
   private double range;
   private int speed;
   private int played = 0;
   private double damage;
   private double sacrifice;
   private int fireTicks;
   public float radius = 2.0F;
   public float grow = 0.05F;
   public double radials = 0.19634954084936207D;
   public int circles = 3;
   public int helixes = 4;
   protected int step = 0;
   private long currentLevel;
   public double selfDamage;
   private double collisionRadius;

   public Immolate(Player player) {
      super(player);
      if (this.bPlayer.canBend(this)) {

         int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
         this.currentLevel = TLBMethods.limitLevels(this.player, statLevel);

         this.direct = player.getLocation().getDirection();
         this.pulse = player.getLocation().clone().add(0.0D, 1.0D, 0.0D);
         this.origin = player.getLocation().clone().add(0.0D, 1.0D, 0.0D);
         this.cooldown = TLBMethods.getLong("ExtraAbilities.EmeraldJelly.Fire.Immolate.Cooldown", currentLevel);
         this.damage = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Fire.Immolate.Damage", currentLevel);
         this.selfDamage = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Fire.Immolate.SelfDamage", currentLevel);
         this.range = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Fire.Immolate.Range", currentLevel);
         this.speed = 1;
         this.sacrifice = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Fire.Immolate.Sacrifice", currentLevel);
         this.collisionRadius = TLBMethods.getDouble("ExtraAbilities.EmeraldJelly.Fire.Immolate.CollisionRadius", currentLevel);
         this.fireTicks =TLBMethods.getInt("ExtraAbilities.EmeraldJelly.Fire.Immolate.FireTicks", currentLevel);
         this.start();
      }
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public String getDescription() {
      return "Left click to sacrifice your energy into your inner fire and unleash a great blast of power!";
   }

   public Location getLocation() {
      return null;
   }

   public String getName() {
      return "Immolate";
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isSneakAbility() {
      return false;
   }

   public void load() {
      FileConfiguration c = ConfigManager.defaultConfig.get();
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.Damage", 6);
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.SelfDamage", 0.3D);
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.Range", 45);
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.Cooldown", 3000);
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.SacrficeDamage", 8);
      c.addDefault("ExtraAbilities.EmeraldJelly.Fire.Immolate.FireTicks", 5);
      ConfigManager.defaultConfig.save();
      ProjectKorra.plugin.getServer().getScheduler().runTaskLater(ProjectKorra.plugin, new Runnable() {
         public void run() {
            ConfigManager.defaultConfig.save();
         }
      }, 20L);
      ProjectKorra.plugin.getServer().getLogger().info(String.valueOf(this.getName()) + " " + this.getVersion() + " Developed by " + this.getAuthor() + " has been enabled!");
      this.perm = new Permission("bending.ability.immolate");
      ProjectKorra.plugin.getServer().getPluginManager().addPermission(this.perm);
      this.perm.setDefault(PermissionDefault.TRUE);
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new ImmolateListener(), ProjectKorra.plugin);
   }

   public void stop() {
      ProjectKorra.plugin.getServer().getLogger().info(String.valueOf(this.getName()) + " " + this.getVersion() + " Developed by " + this.getAuthor() + " has been disabled!");
      ProjectKorra.plugin.getServer().getPluginManager().removePermission(this.perm);
      super.remove();
   }

   public String getAuthor() {
      return "EmeraldJelly";
   }

   public String getVersion() {
      return "v1.0.1";
   }

   public void progress() {
      if (this.pulse.getBlock().isLiquid()) {
         this.remove();
      } else if (!GeneralMethods.isSolid(this.pulse.getBlock()) && !isWater(this.pulse.getBlock()) && !isLava(this.pulse.getBlock())) {
         if (!this.bPlayer.canBendIgnoreBinds(this)) {
            this.remove();
         } else if (this.origin.distance(this.pulse) > this.range) {
            this.remove();
            this.bPlayer.addCooldown(this);
         } else {
            this.pulse.add(this.direct).multiply((double)this.speed);

            while(this.played <= 2) {
               playCombustionSound(this.origin);
               DamageHandler.damageEntity(this.player, this.sacrifice, this);
               this.sacrificeEffect();
               this.player.setFireTicks(20);
               ++this.played;
            }

            this.callEffect();
            Iterator var2 = GeneralMethods.getEntitiesAroundPoint(this.pulse, this.collisionRadius).iterator();

            while(var2.hasNext()) {
               Entity entity = (Entity)var2.next();
               if (entity instanceof LivingEntity && entity.getUniqueId() != this.player.getUniqueId()) {
                  DamageHandler.damageEntity(entity, this.damage, this);
                  DamageHandler.damageEntity(this.player, selfDamage, this);
                  entity.setFireTicks((int)((double)this.fireTicks * 20.0D));
                  this.playFirebendingParticles(entity.getLocation(), 300, 0.2D, 0.2D, 0.2D);
               }
            }

         }
      } else {
         this.remove();
         this.bPlayer.addCooldown(this);
      }
   }

   public void sacrificeEffect() {
      Location location = this.player.getLocation().add(0.0D, 1.0D, 0.0D);

      for(int x = 0; x < this.circles; ++x) {
         for(int i = 0; i < this.helixes; ++i) {
            double angle = (double)this.step * this.radials + 6.283185307179586D * (double)i / (double)this.helixes;
            Vector v = new Vector(Math.cos(angle) * (double)this.radius, (double)((float)this.step * this.grow), Math.sin(angle) * (double)this.radius);
            rotateAroundAxisX(v, (double)((location.getPitch() + 90.0F) * 0.017453292F));
            rotateAroundAxisY(v, (double)(-location.getYaw() * 0.017453292F));
            location.add(v);
            this.playFirebendingParticles(location, 100, 0.0D, 0.0D, 0.0D);
            location.subtract(v);
         }

         ++this.step;
      }

   }

   public static final Vector rotateAroundAxisY(Vector v, double angle) {
      double cos = Math.cos(angle);
      double sin = Math.sin(angle);
      double x = v.getX() * cos + v.getZ() * sin;
      double z = v.getX() * -sin + v.getZ() * cos;
      return v.setX(x).setZ(z);
   }

   public static final Vector rotateAroundAxisX(Vector v, double angle) {
      double cos = Math.cos(angle);
      double sin = Math.sin(angle);
      double y = v.getY() * cos - v.getZ() * sin;
      double z = v.getY() * sin + v.getZ() * cos;
      return v.setY(y).setZ(z);
   }

   public void callEffect() {
      this.playFirebendingParticles(this.pulse, 200, 0.30000001192092896D, 0.30000001192092896D, 0.30000001192092896D);
      if ((new Random()).nextInt(7) == 0) {
         playFirebendingSound(this.pulse);
      }

   }
}
