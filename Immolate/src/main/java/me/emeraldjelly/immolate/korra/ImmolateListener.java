package me.emeraldjelly.immolate.korra;

import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class ImmolateListener implements Listener {
   @EventHandler
   public void onSwing(PlayerAnimationEvent event) {
      if (!event.isCancelled()) {
         if (!CoreAbility.hasAbility(event.getPlayer(), Immolate.class)) {
            new Immolate(event.getPlayer());
         }
      }
   }
}
