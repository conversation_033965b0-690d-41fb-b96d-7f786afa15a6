package com.Pride.korra.LightningOrb;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.ability.LightningAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.permissions.Permission;
import org.bukkit.permissions.PermissionDefault;
import org.bukkit.util.Vector;

public class Bolts extends LightningAbility implements com.projectkorra.projectkorra.ability.AddonAbility
{
  private static final int MAX_TICKS = 10000;
  private static String path = "ExtraAbilities.WhiteXShadow.LightningOrb.Bolts.";
  private int ticks;
  private long cooldown;
  private double speedFactor;
  private double range;
  private double speed;
  private Location location;
  private Location origin;
  private Vector direction;
  public boolean start;
  private double radius;
  private double damage;
  private boolean setShowParticles = true;
  
  public Bolts(Location location, Vector direction, Player player) {
    super(player);
    
    setFields();
    
    this.location = location.clone();
    origin = location.clone();
    range = 10;
    this.direction = direction.clone().normalize();
    
    start();
  }
  
  public Bolts(Player player) {
    super(player);
    
    if (bPlayer.isOnCooldown("Bolts") || bPlayer.isOnCooldown("LightningOrb")) {
      return;
    }
    
    setFields();
    range = 10;
    location = player.getEyeLocation();
    origin = player.getEyeLocation();
    direction = player.getEyeLocation().getDirection().normalize();
    location = location.add(direction.clone());
    
    start();
    
  }
  
  private void setFields() {
    cooldown = ConfigManager.getConfig().getLong(path + "Cooldown");
    range = ConfigManager.getConfig().getDouble(path + "Range");
    speed = ConfigManager.getConfig().getDouble(path + "Speed");
    radius = ConfigManager.getConfig().getDouble(path + "Radius");
    damage = ConfigManager.getConfig().getDouble(path + "Damage");
    
    if (player.hasPermission("bending.ability.LightningOrb")) {
      start = true;
    }
  }
  
  private void advanceLocation() {
    //if ((start) && (setShowParticles)) {
     playLightningbendingParticle(location, 0.1F, 0.1F, 0.1F);
	//GeneralMethods.displayColoredParticle(location, "01E1FF", 0.1F, 0.1F, 0.1F);
    //}
    location = location.add(direction.clone().multiply(speedFactor));
  }
  
  private void damage(Entity entity) {
    if ((entity.getUniqueId() != player.getUniqueId()) && 
      ((entity instanceof LivingEntity))) {
      com.projectkorra.projectkorra.util.DamageHandler.damageEntity(entity, damage, this);
      AirAbility.breakBreathbendingHold(entity);
    }
  }
  

  public void progress()
  {
    if ((!bPlayer.canBendIgnoreBindsCooldowns(this)) || (GeneralMethods.isRegionProtectedFromBuild(this, location))) {
      remove();
      return;
    }
    
    speedFactor = (speed * (ProjectKorra.time_step / 1000.0D));
    ticks += 1;
    
    if (ticks > 10000) {
      remove();
      return;
    }
    
    if (location.distanceSquared(origin) > range * range) {
      remove();
      return;
    }
    
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(location, radius)) {
      damage(entity);
      if ((entity instanceof LivingEntity)) {
        break;
      }
    }
    
    advanceLocation();
    
    bPlayer.addCooldown("Bolts", cooldown);
    
  }
  
  public boolean isHiddenAbility()
  {
    return true;
  }
  
  public String getName()
  {
    return "Bolts";
  }
  
  public Location getLocation()
  {
    return location != null ? location : origin;
  }
  
  public long getCooldown()
  {
    return cooldown;
  }
  
  public boolean isSneakAbility()
  {
    return true;
  }
  
  public boolean isHarmlessAbility()
  {
    return false;
  }
  
  public String getDescription()
  {
    return "Shoot a tiny blast in which allows your allies to glow and to be \"revealed\" and provides a light barrier that gives resistance.";
  }
  
  public String getInstructions()
  {
    return "Left click to shoot a blast of light.";
  }
  

  public String getAuthor()
  {
    return "Prride";
  }
  

  public String getVersion()
  {
    return "Build v1.0";
  }
  
  public void setRange(double range) {
    this.range = range;
  }
  
  public void setShowParticles(boolean showParticles) {
    setShowParticles = showParticles;
  }
  

  public void load()
  {
	  ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new LightOrbListener(), ProjectKorra.plugin); 
	  
    ConfigManager.getConfig().addDefault(path + "Cooldown", Integer.valueOf(0));
    ConfigManager.getConfig().addDefault(path + "Damage", Integer.valueOf(5));
    ConfigManager.getConfig().addDefault(path + "Range", Integer.valueOf(20));
    ConfigManager.getConfig().addDefault(path + "Speed", Integer.valueOf(20));
    ConfigManager.getConfig().addDefault(path + "Radius", Integer.valueOf(2));
    ConfigManager.defaultConfig.save();

  }
  
  public void stop()
  {
	ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
    super.remove();
  }
  

  public boolean isExplosiveAbility()
  {
    return false;
  }
  

  public boolean isIgniteAbility()
  {
    return false;
  }
}
