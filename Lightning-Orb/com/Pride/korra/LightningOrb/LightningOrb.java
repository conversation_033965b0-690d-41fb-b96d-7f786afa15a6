package com.Pride.korra.LightningOrb;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.LightningAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ActionBar;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import java.util.ArrayList;
import net.md_5.bungee.api.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class LightningOrb extends LightningAbility implements AddonAbility
{
  private static String path = "ExtraAbilities.WhiteXShadow.LightningOrb.";
  private long cooldown;
  private boolean launched;
  private double boltRange;
  private ArrayList<Player> update = new ArrayList<Player>();
  private ArrayList<Bolts> bolts;
  private double power;
  private double maxPower;
  private Location location;
  private Vector direction;
  private double range;
  private double travel;
  private TempBlock orb;
  private double damage;
  private double speed;
  private boolean burst;
  private long currentLevel;
  
  public LightningOrb(Player player) {
    super(player);
    
    if ((!bPlayer.canBend(this)) || (!bPlayer.canLightningbend())) {
      return;
    }
    
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
	long currentLevel = TLBMethods.limitLevels(player, statLevel);
    
	this.cooldown = TLBMethods.getLong(path + "Cooldown", currentLevel);
    this.damage = TLBMethods.getDouble(path + "Damage", currentLevel);
    this.speed = TLBMethods.getDouble(path + "Speed", currentLevel);
    this.range = TLBMethods.getDouble(path + "Range", currentLevel);
    ConfigManager.getConfig().getString(path + "Particle");
    this.boltRange = TLBMethods.getDouble(path + "BoltRange", currentLevel);
    this.bolts = new ArrayList<Bolts>();
    this.maxPower = TLBMethods.getDouble(path + "MaxRange", currentLevel);
    this.location = player.getLocation().add(0.0D, -1.0D, 0.0D);
    this.direction = player.getLocation().getDirection();
    update.add(player);
    
    start();
  }
  
  public boolean charged() {
    return launched;
  }
  

  public long getCooldown()
  {
    return cooldown;
  }
  

  public Location getLocation()
  {
    return null;
  }
  

  public String getName()
  {
    return "LightningOrb";
  }
  

  public boolean isHarmlessAbility()
  {
    return false;
  }
  

  public boolean isSneakAbility()
  {
    return true;
  }
  
  public void sendMessage(String message, Player player) {
	  ActionBar.sendActionBar(message , player);
  }
  

  public void progress() {
      if (this.player.isSneaking()) {
    	  
    	  if ((!bPlayer.canBend(this)) || (!bPlayer.canLightningbend())) {
    	      return;
    	    }
    	  
          this.power += 0.05;
          this.power = ((this.power > this.maxPower) ? this.maxPower : this.power);
          
          playLightningbendingParticle(player.getEyeLocation().add(0.0D, -1.0D, 0.0D), (float)power / 10.0F, (float)power / 10.0F, (float)power / 10.0F);
          
          
          if (this.update.contains(this.player)) {
              this.location = this.player.getLocation().add(0.0, 1.0, 0.0);
              this.direction = this.player.getLocation().getDirection();
          }
          else {
              this.advanceOrb();
          }
          if (this.power == this.maxPower) {
              this.launched = true;
              this.sendMessage(new StringBuilder().append(Element.LIGHTNING.getColor()).append(ChatColor.BOLD).append(ChatColor.UNDERLINE).append("CHARGED").toString(), this.player);
          }
      }
      else {
          if (this.burst) {
              this.burst();
          }
          if (this.launched) {
              this.power = 0.0;
          }
      }
  }
  
  public void advanceOrb() {
      for (int i = 0; i < 0.03; ++i) {
          this.update.remove(this.player);
          ++this.travel;
          if (this.travel >= this.range) {
        	  this.bPlayer.addCooldown(this);
        	  this.bPlayer.addCooldown("Bolts", cooldown);
              this.remove();
              return;
          }
          if (this.travel < this.range) {
              this.burst = true;
          }
          if (GeneralMethods.isRegionProtectedFromBuild(this.player, "LightningOrb", this.location)) {
              this.remove();
              return;
          }
      
      if (!bPlayer.canBend(getAbility("LightningOrb"))) {
        	remove();
    		return;
      }
      //GeneralMethods.displayColoredParticle(location, "01E1FF");
      playLightningbendingParticle(location);
 
      ParticleEffect.CRIT_MAGIC.display(location, 1, (float)power / 10.0F, (float)power / 10.0F, (float)power / 10.0F, (float)power / 0.0F);
      //ParticleEffect.MAGIC_CRIT.display(location, (float)power / 10.0F, (float)power / 10.0F, (float)power / 10.0F, 0.0F, 10);
      location = location.add(direction.clone().multiply(speed));
      
      FireAbility.playLightningbendingSound(location);
      orb = new TempBlock(location.getBlock(), Material.CYAN_STAINED_GLASS);
      orb.setRevertTime(100L);
      
      for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, 2.0)) {
          if (entity instanceof LivingEntity && entity.getEntityId() != this.player.getEntityId() && !(entity instanceof ArmorStand)) {
              this.burst();
              DamageHandler.damageEntity(entity, this.damage, this);
              this.bPlayer.addCooldown(this);
              this.bPlayer.addCooldown("Bolts", cooldown);
              this.remove();
              return;
          }
      }
  }
}

private void burst() {
  if (this.burst) {
      final double r = 1.0;
      for (double theta = 0.0; theta <= 180.0; theta += 20.0) {
          for (double dphi = 20.0 / Math.sin(Math.toRadians(theta)), phi = 0.0; phi < 360.0; phi += dphi) {
              final double rphi = Math.toRadians(phi);
              final double rtheta = Math.toRadians(theta);
              final double x = r * Math.cos(rphi) * Math.sin(rtheta);
              final double y = r * Math.sin(rphi) * Math.sin(rtheta);
              final double z = r * Math.cos(rtheta);
              final Vector direction = new Vector(x, z, y);
              final Bolts bolt = new Bolts(this.location, direction.normalize(), this.player);
              bolt.setRange(this.boltRange);
              this.bolts.add(bolt);
          }
      }
      this.bPlayer.addCooldown(this);
      this.bPlayer.addCooldown("Bolts", cooldown);
  }
  this.remove();
  this.smoothParticles();
}

public void smoothParticles() {
  for (int i = 0; i < this.bolts.size(); ++i) {
      final Bolts bolt = this.bolts.get(i);
      final int toggleTime = (int)(i % 1.0);
      new BukkitRunnable() {
          public void run() {
              bolt.setShowParticles(true);
          }
      }.runTaskLater(ProjectKorra.plugin, (long)toggleTime);
  }
}

public String getAuthor() {
  return "WhiteXShadow & Pride";
}

public String getVersion() {
  return "LightningOrb Build V1.0";
}

public String getDescription() {
  return "Charge it up and then left click to launch an orb of lightning. Release sneak to deal to have it explode and launch lightning all around it.";
}

public void load() {
  ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
  ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new LightOrbListener(), ProjectKorra.plugin);
  ConfigManager.getConfig().addDefault(String.valueOf(LightningOrb.path) + "Cooldown", (Object)12000);
  ConfigManager.getConfig().addDefault(String.valueOf(LightningOrb.path) + "Damage", (Object)4);
  ConfigManager.getConfig().addDefault(String.valueOf(LightningOrb.path) + "Speed", (Object)1.4);
  ConfigManager.getConfig().addDefault(String.valueOf(LightningOrb.path) + "Range", (Object)30);
  ConfigManager.getConfig().addDefault(String.valueOf(LightningOrb.path) + "BoltRange", (Object)6);
  ConfigManager.defaultConfig.save();
}

public void stop() {
  ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " stopped! ");
  super.remove();
}
}

