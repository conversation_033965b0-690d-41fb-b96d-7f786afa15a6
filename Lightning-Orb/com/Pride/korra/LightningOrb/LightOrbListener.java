package com.Pride.korra.LightningOrb;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class LightOrbListener implements org.bukkit.event.Listener
{
  public LightOrbListener() {}
  
  @org.bukkit.event.EventHandler
  public void onSneak(PlayerToggleSneakEvent event)
  {
    if (event.isCancelled()) {
      return;
    }
    if (!event.isSneaking()) {
      return;
    }
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if ((bPlayer != null) && (bPlayer.canBend(CoreAbility.getAbility("LightningOrb"))) && (CoreAbility.getAbility(event.getPlayer(), LightningOrb.class) == null)) {
    		new LightningOrb(event.getPlayer());
    	
    }
  }
  
  @EventHandler(priority = EventPriority.NORMAL)
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    }
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer == null)
      return;
    
    if (CoreAbility.hasAbility(event.getPlayer(), LightningOrb.class)) {
      LightningOrb lightningOrb = (LightningOrb)CoreAbility.getAbility(event.getPlayer(), LightningOrb.class);
      if (lightningOrb.charged()) {
        lightningOrb.advanceOrb();
      }
    }
    
    if (CoreAbility.hasAbility(event.getPlayer(), Bolts.class)) {
    	Bolts Bolts = (Bolts)CoreAbility.getAbility(event.getPlayer(), Bolts.class);
        new Bolts(event.getPlayer());
      }
  }
}
