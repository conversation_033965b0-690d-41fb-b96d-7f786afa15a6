package com.thelastblockbender.flamerush;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ComboAbility;
import com.projectkorra.projectkorra.ability.FireAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager.AbilityInformation;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.permissions.Permission;
import org.bukkit.permissions.PermissionDefault;
import org.bukkit.util.NumberConversions;
import org.bukkit.util.Vector;

public class FlameRush extends FireAbility implements AddonAbility, ComboAbility {
  private final Set<Entity> hitEntities = new HashSet<>();

  private Location origin;
  private Location loc;
  private Vector direction;
  private boolean hasFired;
  private boolean createFireTrail;
  private boolean fullyCharged;
  private long cooldown;
  private double damage;
  private double speed;
  private double control;
  private double range;
  private double radius;
  private double collisionRadius;
  private double knockup;
  private double knockback;
  private int fireTicks;
  private double phi;
  private double t;

  private double flameAmount;

  public FlameRush(Player player) {
    super(player);
    if (!bPlayer.canBendIgnoreBinds(this) || hasAbility(player, FlameRush.class)) {
      return;
    }

    setFields();
    start();
  }
  private void setFields() {
    this.loc = player.getEyeLocation().clone();
    this.hasFired = false;
    this.fullyCharged = false;
    this.t = 0;
    this.phi = 0;
    this.flameAmount = 4;
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);
    this.cooldown = TLBMethods.getLong("ExtraAbilities.Sorin.FlameRush.Cooldown", currentLevel);
    this.damage = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Damage", currentLevel);
    this.speed = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Speed", currentLevel);
    this.control = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Control", currentLevel);
    this.range = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Range", currentLevel);
    this.radius = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Radius", currentLevel);
    this.collisionRadius = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.CollisionRadius", currentLevel);
    this.knockup = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Knockup", currentLevel);
    this.knockback = TLBMethods.getDouble("ExtraAbilities.Sorin.FlameRush.Knockback", currentLevel);
    this.fireTicks = TLBMethods.getInt("ExtraAbilities.Sorin.FlameRush.FireTicks", currentLevel);
    this.createFireTrail = ConfigManager.getConfig().getBoolean("ExtraAbilities.Sorin.FlameRush.CreateFlames");
  }
  @Override
  public long getCooldown() {
    return cooldown;
  }
  @Override
  public Location getLocation() {
    return loc;
  }
  @Override
  public String getName() {
    return "FlameRush";
  }
  @Override
  public boolean isHarmlessAbility() {
    return false;
  }
  @Override
  public boolean isSneakAbility() {
    return true;
  }
  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
      remove();
      return;
    }
    if (!bPlayer.getBoundAbilityName().equalsIgnoreCase("FireBlast")) {
      remove();
      return;
    }

    if (ThreadLocalRandom.current().nextInt(3) == 0) {
      FireAbility.playFirebendingSound(loc);
    }
    if (player.isSneaking() && !hasFired) {
      bPlayer.addCooldown(this);
      origin = player.getEyeLocation().clone();
      direction = player.getEyeLocation().getDirection();
      hasFired = true;
      return;
    }
    if (flameAmount < 16) {
      flameAmount += 0.2;
    }
    if (!fullyCharged && radius < 1.4) { // Fully charged in ~3s
      radius += 0.015;
    } else {
      fullyCharged = true;
    }
    if (hasFired) {
      advanceLocation();
      render();
      handleDamage();
    } else {
      loc = player.getEyeLocation();
      if (isInvalidBlock(loc.getBlock())) {
        remove();
        return;
      }
      playChargeAnimation();
    }
  }
  private boolean isInvalidBlock(Block block) {
    return GeneralMethods.isSolid(block) || isWater(block);
  }
  private void advanceLocation() {
    // Alter direction a little bit based on where the player is looking
    Vector dir = player.getEyeLocation().getDirection().multiply(control);
    if (direction.clone().add(dir).lengthSquared() > 0) {
      direction = direction.add(dir).normalize();
    }
    // We use a high speed so it's best to check the intermediate block for collisions
    Block projected = loc.clone().add(direction.clone().multiply(0.5 * speed)).getBlock();
    if (isInvalidBlock(projected) || GeneralMethods.checkDiagonalWall(loc, direction)) {
      remove();
      return;
    }
    loc.add(direction.clone().multiply(speed));
    if (origin.distanceSquared(loc) > range * range) {
      remove();
      return;
    }
    if (createFireTrail) {
      for (Block b : GeneralMethods.getBlocksAroundPoint(loc, collisionRadius)) {
        if (isIgnitable(b) && !GeneralMethods.isRegionProtectedFromBuild(this, loc)) {
          createTempFire(b.getLocation());
        }
      }
    }
  }
  private void handleDamage() {
    for (Entity e : GeneralMethods.getEntitiesAroundPoint(loc, collisionRadius)) {
      if (e instanceof LivingEntity && e.getEntityId() != player.getEntityId() && !(e instanceof ArmorStand)) {
        if (e instanceof Player && Commands.invincible.contains((e).getName())) {
          continue;
        }
        if (!hitEntities.contains(e)) {
          hitEntities.add(e);
          DamageHandler.damageEntity(e, player, damage, this);
          if (e.getFireTicks() < fireTicks) {
            e.setFireTicks(fireTicks);
          }
          e.setVelocity(direction.clone().normalize().setY(knockup).multiply(knockback));
        }
      }
    }
  }

  @Override
  public FlameRush createNewComboInstance(Player p) {
    return new FlameRush(p);
  }

  @Override
  public ArrayList<AbilityInformation> getCombination() {
    ArrayList<AbilityInformation> combo = new ArrayList<>();
    combo.add(new AbilityInformation("FireBurst", ClickType.SHIFT_DOWN));
    combo.add(new AbilityInformation("FireBurst", ClickType.LEFT_CLICK));
    combo.add(new AbilityInformation("FireBurst", ClickType.SHIFT_UP));
    combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_DOWN));
    combo.add(new AbilityInformation("FireBlast", ClickType.SHIFT_UP));
    return combo;
  }
  @Override
  public String getInstructions() {
    return "FireBurst (Hold Sneak) > FireBurst (Left Click) > FireBurst (Release Sneak) > FireBlast (Tap Sneak) *To fire simply tap sneak after holding for desired charge*";
  }
  @Override
  public boolean isHiddenAbility() {
    return false;
  }
  private void render() {
    phi += Math.PI / 20;
    double r = 0.6 * radius;
    Vector offset = new Vector(r, r, r).rotateAroundAxis(direction, phi);
    playFirebendingParticles(loc.clone().add(offset), 1, 0, 0, 0);
    playFirebendingParticles(loc.clone().subtract(offset), 1, 0, 0, 0);
    playFirebendingParticles(loc, NumberConversions.ceil(flameAmount), radius / 5, radius / 5, radius / 5);
  }
  private void playChargeAnimation() {
    t += Math.PI / 24;
    double sin = Math.sin(t);
    double cos = Math.cos(t);
    double x = radius * cos;
    double z = radius * sin;
    Location loc1 = loc.clone().add(x, radius * sin, z);
    playFirebendingParticles(loc1, 1, 0, 0, 0);
    Location loc2 = loc.clone().subtract(x, radius * cos, z);
    playFirebendingParticles(loc2, 1, 0, 0, 0);
    double comp = radius * cos * sin;
    double cosSquared = radius * cos * cos;
    playFirebendingParticles(loc.clone().subtract(cosSquared, radius * sin, comp), 1, 0, 0, 0);
    double sinSquared = radius * sin * sin;
    playFirebendingParticles(loc.clone().add(comp, radius * cos, sinSquared), 1, 0, 0, 0);
    if (fullyCharged) {
      ParticleEffect.SMOKE_LARGE.display(loc1, 1, 0, 0, 0);
      ParticleEffect.SMOKE_LARGE.display(loc2, 1, 0, 0, 0);
    }
  }
  @Override
  public String getDescription() {
    return "Merge two streams of fire into a single powerful attack that deals damage and knockback to all enemies it passes through. The longer you hold before launching, the stronger the attack will be!";
  }

  @Override
  public String getAuthor() {
    return "Soringaming";
  }

  @Override
  public String getVersion() {
    return "v2.1";
  }

  @Override
  public void load() {
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Cooldown", 12000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Damage", 1.7);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Range", 25);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Speed", 1.2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Control", 0.3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Radius", 0.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.CollisionRadius", 0.75);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Knockback", 0.75);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Knockup", 0.1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.Knockback", 0.8);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.FireTicks", 15);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Sorin.FlameRush.CreateFlames", false);
    ConfigManager.defaultConfig.save();
    if (ProjectKorra.plugin.getServer().getPluginManager().getPermission("bending.ability.flamerush") == null) {
      Permission perm = new Permission("bending.ability.flamerush", PermissionDefault.TRUE);
      ProjectKorra.plugin.getServer().getPluginManager().addPermission(perm);
    }
    ProjectKorra.log.info("Successfully enabled " + getName() + " " + getVersion() + " by " + getAuthor());
  }
  @Override
  public void stop() {
    ProjectKorra.log.info("Successfully disabled " + getName() + " " + getVersion() + " by " + getAuthor());
    super.remove();
  }
}