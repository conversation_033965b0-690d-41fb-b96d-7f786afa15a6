package com.thelastblockbender.earthsonar;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.lang.Math;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.EarthAbility;
import com.projectkorra.projectkorra.ability.PassiveAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import me.moros.hyperion.methods.CoreMethods;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.Vibration;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.data.BlockData;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerInteractEvent;

public class EarthSonar extends EarthAbility implements AddonAbility, PassiveAbility {
  private Location location;

  private long cooldown;
  private double range;
  private double delay;
  private int maxDetectors;
  private int particleSpeed;

  private long startTime; //time at the start of the move

  public EarthSonar(Player player, PlayerInteractEvent event) {
    super(player);
    if (!this.bPlayer.canBendPassive(this) || !this.bPlayer.canUsePassive(this)) { //ensure player bending is valid
      return;
    }
    if (!isEarthbendable(event.getClickedBlock())) {return;} //ensure block clicked is earthbendable

    setFields(); //import values
    if (prepare()) { //if prepare method runs without problems, add the cooldown and start the move.
      bPlayer.addCooldown(this);
      
      spawnVibration();      

      player.getLocation().getWorld().playSound(player.getLocation(),Sound.ENTITY_WARDEN_HEARTBEAT, 1f, 2f); //play a sound
      start();
    }
  }

  public void setFields() { //import values
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Thel.Earth.EarthSonar.Cooldown", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.Thel.Earth.EarthSonar.Range", currentLevel);
    delay = TLBMethods.getDouble("ExtraAbilities.Thel.Earth.EarthSonar.Delay", currentLevel);
    cooldown += delay; //the cooldown also covers the duration of the time it takes for sonar to finish.
    maxDetectors = TLBMethods.getInt("ExtraAbilities.Thel.Earth.EarthSonar.MaxDetectors", currentLevel);
    particleSpeed = TLBMethods.getInt("ExtraAbilities.Thel.Earth.EarthSonar.ParticleSpeed", currentLevel);
    startTime = System.currentTimeMillis(); //time at the start of the move
  }

  private void spawnVibration() {
    final float spread = 2;
    final float depth = 0.05f;
    Location ploc = player.getLocation();
    double plocX = ploc.getX();
    double plocY = ploc.getY();
    double plocZ = ploc.getZ();
    double targetX;
    double targetY = player.getLocation().getY() - depth;
    double targetZ;

    List<Player> playerList = Arrays.asList(player); //list of players to see particle (just user)

    for (int i=0;i<8;i++) {
      targetX = plocX + spread * Math.cos(Math.toRadians((i)*(360/8)));
      targetZ = plocZ + spread * Math.sin(Math.toRadians((i)*(360/8)));

      Location targetLocation = new Location(player.getWorld(), targetX, targetY, targetZ);
      Vibration tremorData = new Vibration(new Vibration.Destination.BlockDestination(targetLocation.getBlock()), particleSpeed );
      player.getWorld().spawnParticle(Particle.VIBRATION,playerList,null,plocX,plocY,plocZ,10,0.0,0.0, 0.0,0.0,tremorData);
    }
  }

  private void drawEcho(Player player, Entity target) {
    Vibration echoData = new Vibration(new Vibration.Destination.BlockDestination(player.getLocation().getBlock()), particleSpeed  );
    List<Player> playerList = Arrays.asList(player);
    player.getWorld().spawnParticle(Particle.VIBRATION,playerList,null,target.getLocation().getX(),target.getLocation().getY() + 0.1,target.getLocation().getZ(),10,0.0,0.0, 0.0,0.0,echoData);
  }

  private boolean isValidTarget(Entity entity) {
    if (entity.getUniqueId().equals(player.getUniqueId())) {
      return false;
    }
    if (entity.getType() != EntityType.ARMOR_STAND && entity instanceof LivingEntity) {
      return entity.isOnGround() && isEarthbendable(entity.getLocation().getBlock().getRelative(BlockFace.DOWN));
    }
    return false;
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendPassive(this) || !player.isOnGround() || !isEarthbendable(location.getBlock().getRelative(BlockFace.DOWN))) { //check the user is still on the ground. if not, the move fails and ends.
      remove();
      return;
    }
    if (System.currentTimeMillis()-startTime >= delay) {

      List<Entity> entities = GeneralMethods.getEntitiesAroundPoint(location, range).stream() //sort and filter list
        .filter(this::isValidTarget)
        .sorted(Comparator.comparingDouble(e -> e.getLocation().distanceSquared(location)))
        .limit(maxDetectors).toList();
      for (Entity target : entities) {
        drawEcho(player,target); //draw an echo from the hit entity to the user
        player.playSound(target.getLocation(),Sound.ENTITY_WARDEN_HEARTBEAT, 2f, 1f); //play a sound
      }
      remove();
      return;
      }
  }

  public boolean prepare() {
    location = player.getLocation();
    Block below = location.getBlock().getRelative(BlockFace.DOWN);
    boolean returnStatement = true;
    if (!isEarthbendable(below) ||  !player.isOnGround()) {
      returnStatement = false; //invalid block for sonar to be sent out
    }
    Block block = location.getBlock();
    if (block.isLiquid() || !isTransparent(block)) { //check if not standing in clear space
      returnStatement = false;
    }
    return returnStatement;
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "EarthSonar";
  }

  @Override
  public String getDescription() {
    return "This ability allows a grounded earthbender to detect the presence of those around them.";
  }

  @Override
  public String getInstructions() {
    return "Right click on an earthbendable block to send out a sonic tremor, and wait for it to return - if it comes back, there's something in that direction. Be sure to stay grounded while waiting for the echo!";
  }

  @Override
	public boolean isSneakAbility() {
		return false;
	}
  
  @Override
  public boolean isHarmlessAbility() {
    return true;
  }

  @Override
  public boolean isHiddenAbility() {
    return false;
  }

  @Override
	public boolean isInstantiable() {
		return false;
	}

	@Override
	public boolean isProgressable() {
		return true;
	}

  @Override
  public String getAuthor() {
    return "Thel";
  }

  @Override
  public String getVersion() {
    return "v1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new EarthSonarListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthSonar.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthSonar.Range", 10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthSonar.Delay", 3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthSonar.MaxDetectors", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Earth.EarthSonar.ParticleSpeed", 7);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
  }
}
