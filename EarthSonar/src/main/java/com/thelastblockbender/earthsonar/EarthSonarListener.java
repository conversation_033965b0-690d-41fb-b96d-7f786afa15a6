package com.thelastblockbender.earthsonar;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.block.Action;

import org.bukkit.event.player.PlayerInteractEvent;


public class EarthSonarListener implements org.bukkit.event.Listener {
  @EventHandler
  public void onPlayerInteract(PlayerInteractEvent event) {
    if (event.isCancelled()) {return;} //ensure nothing disallows this from happening
    if (event.getAction() == Action.RIGHT_CLICK_BLOCK) { //check if the right clicked block is earthbendable
      final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
      if (bPlayer != null) {
        new EarthSonar(event.getPlayer(),event);
      }
    }
  }
}
