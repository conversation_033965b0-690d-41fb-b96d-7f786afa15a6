package com.Pride.korra.rootrush;

import java.util.List;
import java.util.Random;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Creature;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.airbending.Suffocate;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import net.md_5.bungee.api.ChatColor;

public class RootRush extends PlantAbility implements AddonAbility {
	
	private long cooldown;
	private double selectRange;
	
	private Location startLoc;
	private Location location;
	private Vector direction;
	private Block groundBlock;
	private Material blockType;
	private Byte blockByte;
	private Random random;
	private double damage;
	private double radius;
	private int rootDuration;
	private long time;
	private double speed;
	private double rideSpeed;
	private long currentLevel;

	@SuppressWarnings("deprecation")
	public RootRush(Player player) {
		super(player);

		if (bPlayer == null) {
			return;
		}

		if (bPlayer.isOnCooldown(this)) {
			return;
		}

		if (!bPlayer.hasElement(Element.PLANT) && !bPlayer.canUseSubElement(Element.SubElement.PLANT)) {
			return;
		}

		if (player.getLocation().clone().add(0, -1, 0).getBlock().getType().isTransparent()) {
			return;
		}

		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    	long currentLevel = TLBMethods.limitLevels(player, statLevel);

		cooldown = TLBMethods.getLong("ExtraAbilities.Prride.RootRide.Cooldown", currentLevel);
		selectRange = TLBMethods.getDouble("ExtraAbilities.Prride.RootRide.Range", currentLevel);
		damage = TLBMethods.getDouble("ExtraAbilities.Prride.RootRide.Damage", currentLevel);
		radius = TLBMethods.getDouble("ExtraAbilities.Prride.RootRide.Radius", currentLevel);
		speed = TLBMethods.getDouble("ExtraAbilities.Prride.RootRide.RootSpeed", currentLevel);
		rideSpeed = TLBMethods.getDouble("ExtraAbilities.Prride.RootRide.RideSpeed", currentLevel);
		direction = player.getLocation().getDirection().clone().normalize().multiply(speed);
		location = player.getLocation();
		this.startLoc = player.getLocation();
		random = new Random();
		rootDuration = TLBMethods.getInt("ExtraAbilities.Prride.RootRide.RootStunDuration", currentLevel);
		time = System.currentTimeMillis();

		start();
	}
	
	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return null;
	}

	@Override
	public String getName() {
		return "RootRush";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return false;
	}

	@Override
	public void progress() {
		groundBlock = getGround();
		if (groundBlock == null) {
			remove();
			return;
		}
		direction = direction.clone().normalize().multiply(1.5);
		direction.setY(0);
		double distance = location.getY() - (double) groundBlock.getY();
		double dx = Math.abs(distance - 2.4);
		if (distance > 1.75) {
			direction.setY(-.50 * dx * dx);
		} else if (distance < 1) {
			direction.setY(.50 * dx * dx);
		} else {
			direction.setY(0);
		}
		location.add(direction);
		if (player.isDead() || !player.isOnline()) {
			bPlayer.addCooldown(this);
			remove();
			return;
		}
		if (location.getWorld().equals(startLoc.getWorld()) && location.distance(startLoc) >= selectRange) {
			bPlayer.addCooldown(this);
			remove();
			return;
		}
		if (System.currentTimeMillis() > time + 2000L) {
			bPlayer.addCooldown(this);
			remove();
			return;
		} else {
			if (player.isSneaking()) {
				Vector velocity = direction.clone().normalize().multiply(rideSpeed);
				player.setVelocity(velocity);
			}
		}
		if (blockType == null) {
			return;
		} else if (blockByte == null) {
			return;
		}
		ItemStack dirt = new ItemStack(Material.DIRT);
		ParticleEffect.ITEM_CRACK.display(location.add(0,0,0), 27, 1, 0.1, 1, 0.1, dirt);
		//ParticleEffect.BLOCK_CRACK.display((ParticleEffect.ParticleData) new ParticleEffect.BlockData(Material.DIRT, blockByte), 1F, 0.1F, 1F, 0.1F, 40, location.add(0, 0, 0), 500);
		ItemStack wood = new ItemStack(Material.OAK_WOOD);
		ParticleEffect.ITEM_CRACK.display(location.add(0,0,0), 40, 1, 0.1, 1, 0.1, wood);
		//ParticleEffect.BLOCK_CRACK.display((ParticleEffect.ParticleData) new ParticleEffect.BlockData(Material.WOOD, blockByte), 1F, 0.1F, 1F, 0.1F, 60, location.add(0, 0, 0), 500);
		
		for (Entity e : GeneralMethods.getEntitiesAroundPoint(location, radius)) {
			if (e.getEntityId() != player.getEntityId() && e instanceof LivingEntity) {
				ParticleEffect.EXPLOSION_LARGE.display(location, 0, 0, 0, 0, 2);
				//ParticleEffect.LARGE_EXPLODE.display(location, 0f, 0f, 0f, 0, 2);
				DamageHandler.damageEntity(e, damage, this);
				root(e);
			}
		}

		player.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, rootDuration, 1));
		
		if (random.nextInt(2) == 0) {
			location.getWorld().playSound(location, Sound.ENTITY_ZOMBIE_BREAK_WOODEN_DOOR, (float) 0.3, (float) 0.5);
		}
		
	}
	
	public void root(Entity entity) {
		if (entity instanceof Creature) {
			((Creature) entity).setTarget(null);
		}
		
		Player targetPlayer;
		if (entity instanceof Player) {
			if (Suffocate.isChannelingSphere((Player) entity)) {
				Suffocate.remove((Player) entity);
			}
			
			targetPlayer = (Player) entity;
			targetPlayer.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, rootDuration, 1));
		} 
		//MovementHandler mh = new MovementHandler((LivingEntity) entity);
		//mh.stop(rootDuration/1000*20, ChatColor.DARK_GREEN + "* ROOTED *");
		
		String message = "§2§lROOTED";
		com.projectkorra.projectkorra.util.ActionBar.sendActionBar(message, new Player[] { player });
	}
	
	@SuppressWarnings("deprecation")
	private Block getGround() {
		Block b = GeneralMethods.getTopBlock(location, 3);
		if (isEarthbendable(b) || isPlantbendable(b)) {
			blockType = b.getType();
			blockByte = b.getData();
			return b;
		} else {
			while (!isEarthbendable(b) && !isPlantbendable(b)) {
				b = b.getRelative(BlockFace.DOWN);
				if (player.getLocation().getBlockY() - b.getY() > 5) {
					break;
				}
			}
			if (isEarthbendable(b) || isPlantbendable(b)) {
				blockType = b.getType();
				blockByte = b.getData();
				return b;
			}
		}
		return null;
	}
	
	public boolean isEarthbendable(Block block) {
		return isEarthbendable(block.getType());
	}
	
	public static boolean isEarthbendable(Material material) {
		return isEarth(material);
	}
	
	public static boolean isEarth(Material material) {
		return material == Material.DIRT || material == Material.GRASS_BLOCK || material == Material.DIRT_PATH;
	}

	@Override
	public String getAuthor() {
		return "Prride (updated by DeminBell)";
	}

	@Override
	public String getVersion() {
		return "1.2";
	}
	
	@Override
	public String getDescription() {
		return "Summon a frenzy of roots at whatever you're looking at! Once it hits a target, they become rooted to the ground whilst taking damage. You can sneak to ride on the roots as a form of transportation, however you will only move to the location you originally looked at. In other words, it is not controllable.";
	}
	
	@Override
	public String getInstructions() {
		return "Left click to summon a frenzy of roots at your desired location. However, you can also sneak to ride on the roots.";
	}

	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new RootRushListener(), ProjectKorra.plugin);
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded! ");
		
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.Cooldown", 7500);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.Damage", 5);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.RootStunDuration", 4000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.Radius", 4);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.Range", 30);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.RootSpeed", 1.5);
		ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.RootRide.RideSpeed", 1.5);
		ConfigManager.defaultConfig.save();
	}

	@Override
	public void stop() {
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
		super.remove();
	}

}
