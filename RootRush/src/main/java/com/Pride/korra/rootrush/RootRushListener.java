package com.Pride.korra.rootrush;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class RootRushListener implements Listener {

	@EventHandler
	public void onSwing(PlayerAnimationEvent event) {
		if (event.isCancelled()) {
			return;
		} else if (CoreAbility.hasAbility(event.getPlayer(), RootRush.class)) {
			return;
		}

		Player player = event.getPlayer();
		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);

		if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("RootRush")) {
			new RootRush(player);
		}
	}

}
