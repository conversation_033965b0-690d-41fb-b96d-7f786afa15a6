package dev.shoda.pkabilities.lavageyser;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;

public class LavaGeyserListener implements Listener {
    @EventHandler
    public void onSneak(PlayerInteractEvent event) {
        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            if (event.getPlayer().isSneaking()) {
                BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
                if (bPlayer == null) return;

                String abilityName = bPlayer.getBoundAbilityName();
                if (abilityName.equalsIgnoreCase("LavaGeyser")) {
                    new LavaGeyser(event.getPlayer(), event.getClickedBlock());
                    event.setCancelled(true);
                }
            }
        }
    }
}
