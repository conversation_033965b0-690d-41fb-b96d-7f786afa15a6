package dev.shoda.pkabilities.lavageyser;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.LavaAbility;
import com.projectkorra.projectkorra.attribute.Attribute;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.MemorySection;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;
import org.bukkit.entity.Entity;
import com.projectkorra.projectkorra.GeneralMethods;

import java.util.*;

public class LavaGeyser extends LavaAbility implements AddonAbility {
    private final Map<Block, TempBlock> blocks = new HashMap<>();

    @Attribute(Attribute.SPEED)
    private double speed;
    @Attribute(Attribute.RADIUS)
    private double maxRadius;
    private long intervalMs;
    @Attribute(Attribute.COOLDOWN)
    private long cooldown;
    private long shorterCooldown;
    @Attribute(Attribute.HEIGHT)
    private int pillarHeight;
    @Attribute(Attribute.DURATION)
    private long pillarDuration;
    private double blocksPercentage;
    private long pillarSpeedMs;
    private int maxDepth;
    private int maxHeight;

    private final Block block;
    private long time;
    private int currentRadius;
    private Material defaultNextBlock;
    private Material lastBlock;
    private long startTime; //time at the start of the move
    private boolean skipPool; //if true, skips the formation of the lavapool
    private double hitRadius;

    private double heightFactor; //knockback height

    public LavaGeyser(Player player, Block block) {
        super(player);
        this.block = block;

        skipPool = false;
        if (!bPlayer.canBend(this)) return;
        if (block == null || block.isPassable() || (isEarthbendable(block) == false && block.getType() != Material.MAGMA_BLOCK) || block.getRelative(BlockFace.DOWN).getType().isAir() || block.getRelative(BlockFace.UP).getType() == Material.WATER || block.getRelative(BlockFace.UP).getType() == Material.BUBBLE_COLUMN) {remove(); return;} //if target block is null, liquid/passable, not earthbendable, has air below it or has water above it, dont activate move.
        if (block.getRelative(BlockFace.UP).getType() == Material.LAVA) {skipPool = true; block = block.getRelative(BlockFace.UP);}

        LavaGeyser oldAbility = getAbility(player, LavaGeyser.class);
        if (oldAbility != null) {
            return;
        }
        
        defaultNextBlock = getNextBlock("Others").orElseGet(() -> {
            ProjectKorra.plugin.getLogger().severe("Invalid `ExtraAbilities.Shoda.LavaGeyser.NextBlock.Others`");
            return null;
        });
        lastBlock = getNextBlock("LastBlock").orElseGet(() -> {
            ProjectKorra.plugin.getLogger().severe("Invalid `ExtraAbilities.Shoda.LavaGeyser.NextBlock.LastBlock`");
            return null;
        });
        if (defaultNextBlock == null || lastBlock == null) {
            return;
        }

        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);

        maxRadius = TLBMethods.getInt("ExtraAbilities.Shoda.LavaGeyser.Radius", currentLevel);
        maxDepth = TLBMethods.getInt("ExtraAbilities.Shoda.LavaGeyser.MaxDepth", currentLevel);
        maxHeight = TLBMethods.getInt("ExtraAbilities.Shoda.LavaGeyser.MaxHeight", currentLevel);
        cooldown = TLBMethods.getLong("ExtraAbilities.Shoda.LavaGeyser.Cooldown", currentLevel);
        shorterCooldown = TLBMethods.getLong("ExtraAbilities.Shoda.LavaGeyser.shorterCooldown", currentLevel);
        pillarHeight = TLBMethods.getInt("ExtraAbilities.Shoda.LavaGeyser.PillarHeight", currentLevel);
        pillarDuration = TLBMethods.getLong("ExtraAbilities.Shoda.LavaGeyser.PillarDuration", currentLevel);
        blocksPercentage = TLBMethods.getDouble("ExtraAbilities.Shoda.LavaGeyser.BlocksPercentage", currentLevel);
        pillarSpeedMs = TLBMethods.getLong("ExtraAbilities.Shoda.LavaGeyser.PillarSpeedMs", currentLevel);
        speed = TLBMethods.getDouble("ExtraAbilities.Shoda.LavaGeyser.Speed", currentLevel);
        hitRadius = TLBMethods.getDouble("ExtraAbilities.Shoda.LavaGeyser.HitRadius", currentLevel);
		heightFactor = TLBMethods.getDouble("ExtraAbilities.Shoda.LavaGeyser.HeightFactor", currentLevel);
        intervalMs = (long) (1000.0 / speed);
        startTime = System.currentTimeMillis(); //time at the start of the move

        if (skipPool == true) {cooldown = shorterCooldown; pillarSpeedMs = pillarSpeedMs / 2;} //reduce cooldown if the user triggers the pillar on a pre-existing lava source (and double pillar speed)

        start();
    }

    @Override
    public void progress() {
        if (!bPlayer.canBendIgnoreBinds(this)) { //ensure user can still bend currently
            remove();
            return;
        }

        block.getLocation().getWorld().playSound(block.getLocation(),Sound.BLOCK_BUBBLE_COLUMN_UPWARDS_INSIDE, 0.5f, 0.5f); //play a sound
        long now = System.currentTimeMillis();
        if (now - time >= intervalMs) {
            time = now;
            if (player.getWorld() != block.getWorld()) {
                remove();
                return;
            }
            int y = block.getY();
            Random random = new Random();
            boolean last = false;
            for (int x = block.getX() - currentRadius; x <= block.getX() + currentRadius; x++) {
                if (skipPool == true) {last = true; break;}
                for (int z = block.getZ() - currentRadius; z <= block.getZ() + currentRadius; z++) {
                    Set<Block> nextBlocksY = new HashSet<>();
                    Block targetBlock = new Location(block.getWorld(), x, y, z).getBlock();
                    while (!targetBlock.getRelative(BlockFace.UP).isPassable() && Math.abs(targetBlock.getY() - y) < maxHeight) {
                        targetBlock = targetBlock.getRelative(BlockFace.UP);
                        nextBlocksY.add(targetBlock);
                    }
                    while (targetBlock.isPassable() && Math.abs(targetBlock.getY() - y) < maxDepth) {
                        targetBlock = targetBlock.getRelative(BlockFace.DOWN);
                    }
                    if (isEarthbendable(targetBlock) || targetBlock.getBlockData().getMaterial() == Material.MAGMA_BLOCK) {nextBlocksY.add(targetBlock);} //skip iteration if the block cannot be earthbent
                    Location effectLoc = targetBlock.getLocation(); //get block location
                    effectLoc.getWorld().playSound(effectLoc,Sound.ENTITY_GHAST_SHOOT, 0.05f, 0.5f); //whenever a block is added, play a sound there
                    if (isEarthbendable(targetBlock) || targetBlock.getBlockData().getMaterial() == Material.LAVA || targetBlock.getBlockData().getMaterial() == Material.MAGMA_BLOCK) { //do not draw particles around air blocks that weren't converted to lava
                        ParticleEffect.LAVA.display(effectLoc, 0, 0, 0, 0, 1); //and play a particle
                    }
                    for (Block nextBlock : nextBlocksY) {
                        double distance = Math.sqrt(Math.pow(block.getX() - nextBlock.getX(), 2) + Math.pow(block.getZ() - nextBlock.getZ(), 2));
                        if (distance <= currentRadius) {
                            TempBlock tempBlock = blocks.get(nextBlock);
                            if (random.nextDouble() < blocksPercentage / 100.0) {
                                tempBlock = nextBlock(nextBlock);
                            }
                            if (tempBlock != null) {
                                tempBlock.setRevertTime(pillarDuration);
                            }
                            if (distance + 1 >= maxRadius && nextBlock.getType() == lastBlock) {
                                last = true;
                            }
                            if (System.currentTimeMillis()-startTime >= 4000) {remove(); return;} //end move if 4 seconds have passed with no pillar (avoids infinite loop)
                        }
                    }
                }
            }
            if (last) {
                for (int i = 1; i < pillarHeight + 1; i++) {
                    int finalI = i;
                    ProjectKorra.plugin.getServer().getScheduler().scheduleSyncDelayedTask(ProjectKorra.plugin, () -> {
                        Material matAbove = block.getRelative(BlockFace.UP, finalI).getBlockData().getMaterial();
                        if (matAbove.isAir() || matAbove == Material.LAVA || matAbove == Material.MAGMA_BLOCK || isEarthbendable(block.getRelative(BlockFace.UP, finalI))) { //additional check to make sure the pillar doesnt punch through anything
                            TempBlock tempBlock = new TempBlock(block.getLocation().add(0, finalI, 0).getBlock(), lastBlock);
                            tempBlock.setRevertTime(pillarDuration + (pillarHeight - finalI) * pillarSpeedMs * 2);
                            ParticleEffect.LAVA.display(tempBlock.getLocation(), 2, 0, 2, 0, 50); //and play a particle

                            List<Entity> entities = GeneralMethods.getEntitiesAroundPoint(tempBlock.getLocation(), hitRadius);
                            for (Entity entity : entities) {    
                                if (entity instanceof Player) {
                                    Player target = (Player) entity;
                                    Vector currentVelocity = target.getVelocity();
                                    currentVelocity.add(new Vector(0, heightFactor, 0));
                                    target.setVelocity(currentVelocity);
                                }
                            }
                        }

                        
                    }, i * pillarSpeedMs / 50L);
                }
                remove();
            } else if (currentRadius < maxRadius) {
                currentRadius++;
            }
        }
    }

    public TempBlock nextBlock(Block block) {
        if (block.isPassable() && !block.isLiquid()) return null; 
        Material nextType = getNextBlock(block.getType().name()).orElse(defaultNextBlock);
        if (block.getType() == nextType || block.getType() == lastBlock) {
            return blocks.get(block);
        } else {
            TempBlock oldBlock = blocks.get(block);
            if (oldBlock != null) {
                Material oldType = oldBlock.getBlockData().getMaterial();
                oldBlock.setType(nextType);
                if ((oldBlock.getBlock().getRelative(BlockFace.UP).getType() == Material.WATER || oldBlock.getBlock().getRelative(BlockFace.UP).getType() == Material.BUBBLE_COLUMN) && oldBlock.getBlock().getType() == Material.LAVA) {oldBlock.setType(Material.MAGMA_BLOCK);} //if the block above is water, set block to magma not lava
                TempBlock.RevertTask oldTask = oldBlock.getRevertTask();
                oldBlock.setRevertTask(() -> {
                    ProjectKorra.plugin.getServer().getScheduler().runTask(ProjectKorra.plugin, () -> {
                        TempBlock newOldBlock = new TempBlock(oldBlock.getBlock(), oldType);
                        newOldBlock.setRevertTask(oldTask);
                        newOldBlock.setRevertTime(intervalMs);
                    });
                });
                return oldBlock;
            } else {
                TempBlock tempBlock = new TempBlock(block, nextType);
                tempBlock.setRevertTask(() -> blocks.remove(block));
                blocks.put(block, tempBlock);
                return tempBlock;
            }
        }
    }

    public Optional<Material> getNextBlock(String name) {
        return Optional.ofNullable(ConfigManager.getConfig().getString("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + name)).map(Material::matchMaterial);
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public String getInstructions() {
        return "Hold sneak while looking at an earth bendable block and punch to create a geyser that will slowly grow.";
    }

    @Override
    public String getDescription() {
        return "Summon lava from beneath the ground!";
    }

    @Override
    public long getCooldown() {
        return cooldown;
    }

    @Override
    public String getName() {
        return "LavaGeyser";
    }

    @Override
    public Location getLocation() {
        return block.getLocation();
    }

    @Override
    public void load() {
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.Speed", 2.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.Radius", 4);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.Cooldown", 15000L);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.shorterCooldown", 5000L);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.PillarHeight", 4);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.PillarDuration", 2500L);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.PillarSpeedMs", 200L);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.BlocksPercentage", 90.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + Material.GRASS_BLOCK.name(), Material.PODZOL.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + Material.PODZOL.name(), Material.DIRT.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + Material.DIRT.name(), Material.COARSE_DIRT.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + Material.STONE.name(), Material.MAGMA_BLOCK.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock." + Material.MAGMA_BLOCK.name(), Material.LAVA.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock.Others", Material.MAGMA_BLOCK.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.NextBlock.LastBlock", Material.LAVA.name());
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.MaxDepth", 1);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.MaxHeight", 0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.HitRadius", 0.75);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Shoda.LavaGeyser.HeightFactor", 2);

        ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new LavaGeyserListener(), ProjectKorra.plugin);
        ProjectKorra.plugin.getLogger().info("Successfully enabled " + getName() + " " + getVersion() + " by " + getAuthor());
    }

    @Override
    public void stop() {
        remove();
    }

    @Override
    public String getAuthor() {
        return "TLB (Original: Shoda)";
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public void remove() {
        super.remove();
        if (bPlayer != null) {
            bPlayer.addCooldown(this);
        }
    }
}
