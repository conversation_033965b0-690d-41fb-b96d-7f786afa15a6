package com.thelastblockbender.thornrows;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ElementalAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class ThornRows extends PlantAbility implements AddonAbility {
  private Location location;

  private long cooldown;
  private double radius;
  private double duration;
  private double vegetationThreshold; //% of blocks that must be plantbendable
  private double chargeTime; //time in milliseconds to charge up the move
  private double densityFactor;
  private final static double densityConstant = 62.8318;
  private double density;
  private boolean charged = false; //is the move charged yet?
  private double intensity; //how powerful the move is (determined by % vegetation nearby)
  private double hurtBoxHeight; //height of the hurtbox of the thorns
  private double damage; //tick damage from moving on thorns
  private long timeSinceLastDraw; //time since last time particles were drawn
  private double plantBreakChance;
  private List<Location> pointList = new ArrayList<>(); //list of 3D coordinates for the thorns
  private final Map<Block,List<Location>> pointMap = new HashMap<>(); //more complex hashmap for efficiently calculating collisions
  private boolean finalDrawDone = false; //marks that the last particle refresh has been done (ensures particles disappear at right moment)
  private double wanderLimit; //how far the player can walk away from the starting point of their move while charging it
  private double thornHitDistance; //radius around a thorn in which an entity will get hurt by it
  private double damageCooldown; //how long between each hit
  private final Map<UUID, Long> entityNextHitTimes = new HashMap<>(); //hash map containing all entities that have taken damage from the move, and tracks the time at which they can take damage again
  private boolean beingChiBlocked = false; //is move being chi blocked? (used for animations)

  private long chargeEndTime; //time when the move finishes charging & is released


  public ThornRows(Player player) {
    super(player);
    if (!bPlayer.canBend(this)) return; //ensure player bending is valid

    setFields(); //import values
    if (prepare()) { //if prepare method runs without problems, start the move and add cooldown
      start();
    }
  }

  public void setFields() { //import values
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    cooldown = TLBMethods.getLong("ExtraAbilities.Thel.Water.Plant.ThornRows.Cooldown", currentLevel);
    radius = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.Radius", currentLevel);
    duration = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.Duration", currentLevel);
    densityFactor = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.DensityFactor", currentLevel); //should be from 0 to 1
    density = densityFactor * radius * radius * Math.PI / densityConstant; //scale up density with the radius, proportionally to circle area. constant allows result to sit between 1-5 when densityFactor is 0.2 - 1 (low to high density) when radius is 10, which are some nice values.
    vegetationThreshold = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.VegetationThreshold", currentLevel);
    chargeTime = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.ChargeTime", currentLevel);
    hurtBoxHeight = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.HurtBoxHeight", currentLevel);
    damage = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.Damage", currentLevel);
    plantBreakChance = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.PlantBreakChance", currentLevel);
    damageCooldown = TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.DamageCooldown", currentLevel);
    //for distance checks like the two below, square the value now to avoid doing it in every single future check against distanceSquared
    wanderLimit = Math.pow(TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.WanderLimit", currentLevel), 2);
    thornHitDistance = Math.pow(TLBMethods.getDouble("ExtraAbilities.Thel.Water.Plant.ThornRows.HitDistance", currentLevel), 2);;
  }

  private void thornAppearDisappearAnim(Location thornLoc) { //sequence for when thorns appear and disappear
    player.getWorld().playSound(thornLoc,Sound.BLOCK_MANGROVE_ROOTS_BREAK, 0.15f, 1f);
    ParticleEffect.BLOCK_DUST.display(thornLoc, 1, 0, 0, 0, 0, Material.DIRT.createBlockData());
  }

  private boolean isInsideCircle(double x, double z, double centerX, double centerZ, double radius) {
    double distanceSquared = (x - centerX) * (x - centerX) + (z - centerZ) * (z - centerZ);
    return distanceSquared <= radius * radius;
  }

  private boolean checkVegetation() { //checks the percentage of plants on the layer above the thorny-floor-to-be, decides if enough of them are present for the move to function, and sets the "intensity" score of the move based on the percentage of how many plants could theoretically fit in that space.
   
    int minX = (int)Math.floor(location.getX() - radius);
    int maxX = (int)Math.ceil(location.getX() + radius);
    int minZ = (int)Math.floor(location.getZ() - radius);
    int maxZ = (int)Math.ceil(location.getZ() + radius);

    double plants = 0;
    double spaces = 0;

    for (int x = minX; x <= maxX; x++) {
        for (int z = minZ; z <= maxZ; z++) {
            if (isInsideCircle(x, z, location.getX(), location.getZ(), radius)) {
                Location targetLoc = new Location(location.getWorld(),x,location.getY(),z);
                if (isPlantbendable(targetLoc.getBlock())) {plants +=1;} //add 1 to plant counter if a plant is found on the Y level
                else if (isPlantbendable(targetLoc.clone().add(0,1,0).getBlock())) {plants +=1;} //add 1 to plant counter if a plant is found on the Y+1 level (for slope tolerance)
                else if (isPlantbendable(targetLoc.clone().add(0,-1,0).getBlock())) {plants +=1;} //add 1 to plant counter if a plant is found on the Y-1 level (this elif system means only 1 plant is considered for any given column)
                else if (targetLoc.clone().add(0,-1,0).getBlock().getType() == Material.GRASS_BLOCK || targetLoc.clone().add(0,-1,0).getBlock().getType() == Material.MOSS_BLOCK) {plants +=0.5;} //add 1 to plant counter if a plant is found on the Y-1 level (this elif system means only 1 plant is considered for any given column)
                
                spaces +=1; //either way, add 1 to the total spaces counter
            }
        }
    }

    intensity = plants / spaces; //% of vegetation

    return intensity >= vegetationThreshold; //if intensity is sufficiently high to start the move
  }

  private boolean isValidGround(Location targetLoc) {

    if (GeneralMethods.isRegionProtectedFromBuild(this,targetLoc)) {return false;} //check for region protection

    Material m = targetLoc.getBlock().getType();
    if (ElementalAbility.isGroundPlant(m) || m == Material.DIRT) { //if valid ground block & air/water above
      if (targetLoc.getBlock().getRelative(BlockFace.UP).isPassable()) {//check if block above thorn is clear
        return true;
      }
    } //if the normal block didnt work, try above and below
    //try moving block up:
    targetLoc.add(0,1,0);
    if ((ElementalAbility.isGroundPlant(targetLoc.getBlock().getType()) || targetLoc.getBlock().getType() == Material.DIRT) && targetLoc.getBlock().getRelative(BlockFace.UP).isPassable() ) {return true;} //is block above valid?
    //try moving block down:
    else {
      targetLoc.add(0,-2,0);
      if ((ElementalAbility.isGroundPlant(targetLoc.getBlock().getType()) || targetLoc.getBlock().getType() == Material.DIRT) && targetLoc.getBlock().getRelative(BlockFace.UP).isPassable() ) {return true;} //is block below valid?
      targetLoc.add(0,2,0); //reset it
    }
    return false;
  }

  private void hurtTarget(Entity entity) {
    long time = System.currentTimeMillis();
    entityNextHitTimes.compute(entity.getUniqueId(), (uuid, nextHitTime) -> {
      DamageHandler.damageEntity(entity, damage, this); //apply damage
      entity.getWorld().playSound(entity.getLocation(),Sound.ENTITY_PLAYER_HURT_SWEET_BERRY_BUSH, 0.5f, 1f); //play hit sound at hurt entity's location
      return time + (long) damageCooldown; //update nextHitTime to now + dmg cooldown
    });
  }
  

  private boolean isValidTarget(Entity entity) {

    //check if they are within the vertical hurtbox
    if (!(entity.getLocation().getY() > (location.getY() - (hurtBoxHeight/2)) && entity.getLocation().getY() < (location.getY() + (hurtBoxHeight/2)))) { //ensure they are, vertically, within the hurtbox.
      return false;
    }

    //check if they are a living entity that isnt an armor stand
    if ((entity.getType() == EntityType.ARMOR_STAND || !(entity instanceof LivingEntity))) { //ensure they are a living entity
      return false;
    }
    
    //check if they are not the user
    if (entity.getUniqueId().equals(player.getUniqueId())) { //do not hurt the user
      return false;
    }

    //if their cooldown has not yet elapsed
    long nextHitTime = entityNextHitTimes.getOrDefault(entity.getUniqueId(),0L);
    if (nextHitTime > System.currentTimeMillis()) {//if cooldown is not yet over, their cooldown is not ready, so do not proceed
      return false; 
    }
    //are they standing on a block in the pointMap?
    Block tB = entity.getLocation().getBlock();
    if (!pointMap.containsKey(tB)) {
      return false; //if the entity's block location is NOT in the pointMap as a key, they [most likely] cannot be near a thorn
    }

    //are they within range of any thorn inside that pointMap entry?
    boolean withinRange = false;
    List<Location> nearbyThorns = pointMap.get(tB); //grab list of thorn positons in that block
    for (Location loc: nearbyThorns) { //check if any of them are close enough     
      if (entity.getLocation().distanceSquared(loc) <= thornHitDistance) { //if close enough
        //add them to list of targets to damage
        withinRange = true;
        break; //do not iterate again if a hit occured, can only be hit by a max of one thorn per tick anyways
      }
    }
    if (!withinRange) {return false;}

    //check if they are a plantbending player currently holding shift on ThornRows while not chiblocked
    if (entity.getType() == EntityType.PLAYER) { //if it is a player
      BendingPlayer bPlayer = BendingPlayer.getBendingPlayer((Player) entity);
      if (bPlayer != null && bPlayer.hasElement(Element.PLANT) && bPlayer.getBoundAbilityName().equalsIgnoreCase("ThornRows") && entity.isSneaking() && bPlayer.canBendIgnoreCooldowns(this) ){ //dont hurt plantbenders that are holding shift on thornRows (and are not chiblocked)
        return false;
      }
    }

    return true; //if all checks went through ok
  }

  private void tickDamage() { //should be run every tick. causes damage to entities moving on the thorns.
    GeneralMethods.getEntitiesAroundPoint(location, radius).stream() //filter list and iterate through
        .filter(this::isValidTarget)
        .forEach(this::hurtTarget);
  }

  private boolean generatePoints() { //generates a list of points where thorns will form on the ground.
    for (int i = 0; i < densityFactor * 80; i++ ) {
      double angle = ThreadLocalRandom.current().nextDouble() * 2 * Math.PI;  //angle (rad)
      double distance = Math.sqrt(ThreadLocalRandom.current().nextDouble()) * radius;  //random distance squared (allows uniform distribution)
      
      double xOffset = Math.cos(angle) * distance;
      double zOffset = Math.sin(angle) * distance;
      
      double x = location.getX() + xOffset;
      double z = location.getZ() + zOffset;
      Location targetLoc = new Location(location.getWorld(),x,location.getY(),z);

      if (GeneralMethods.isRegionProtectedFromBuild(this,targetLoc)) {return false;} //check for region protection and end the move if true

      if (!isValidGround(targetLoc.add(0,-1,0))) {continue;} //skip this iteration if the ground is not valid
      targetLoc.add(0,1,0); //reset pos

      thornAppearDisappearAnim(targetLoc);

      pointList.add(targetLoc); //store points in easily iterable fashion
      
      Block tB = targetLoc.getBlock(); //get block that the thorn will be in
      pointMap.computeIfAbsent(tB, block -> new ArrayList<>()) /*retrieve entry or create blank entry*/ .add(targetLoc); //append targetLoc to entry
      
      if (ThreadLocalRandom.current().nextDouble() <= plantBreakChance) { //low chance to remove the plant above when the thorn appears
        if (isPlantbendable(tB)) {tB.setType(Material.AIR);}
      }
    }
    return true;
  }

  private void removeThorns() {//called at the end of the move to animate the removal of the thorns
    for (Location loc : pointList) {
      thornAppearDisappearAnim(loc);
    }
  }

  private void drawThorns() {
    
    for (Location loc: pointList) {
      if (isValidGround(loc.add(0,-1,0))) { //only produce spikes on valid ground
        ParticleEffect.BARRIER.display(loc.add(0,1,0), 0, 0, 0, 0, 0, Material.DEAD_BUSH.createBlockData()); //draw thorn
      }
    }
  }

  @Override
  public void progress() {

    if (charged || (System.currentTimeMillis()-this.getStartTime() >= chargeTime && !player.isSneaking())) { //when move has been charged and released
      if (!charged) { //On charge up finishing: (only runs once)
        location = player.getLocation(); //update location to player's current position when releasing
        densityFactor = densityFactor * ((intensity * 3)+0.5); //density increases with intensity
        damage = damage * (intensity+0.5); //same for damage per tick
        if (!generatePoints()) {remove(); return;} //prepare the list of thorn locations, and end move if it fails
        drawThorns();
        bPlayer.addCooldown(this); //add cooldown
        charged = true;
        chargeEndTime = System.currentTimeMillis(); //start phase 2 timer
        timeSinceLastDraw = chargeEndTime;
      }
    
      //on subsequent iterations:

      if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {finalDrawDone = true; chargeEndTime = ((timeSinceLastDraw + 4000) - (long) duration); beingChiBlocked = true;} //queue move for ending if chiblocked (do NOT end abruptly, blockmarker particles take up to 4 seconds to be decay). do this by setting chargeEndTime to (10-X) seconds ago, such that X seconds remain, where X = time left until next draw

      if (!finalDrawDone && System.currentTimeMillis() - timeSinceLastDraw > 4000) { //only spawn particles every 4 seconds
        drawThorns(); //maintain the number of thorns
        timeSinceLastDraw = System.currentTimeMillis();
      } else if (beingChiBlocked) { //this can only occur if the above condition is false, so i am taking this opportunity to reduce the number of times it'll be checked.
                    for (int i = 0; i < 10; i++) { //a small water spray animation to let player know the chiblock is doing something
                      Location tLoc = pointList.get(ThreadLocalRandom.current().nextInt(pointList.size()));
                      ParticleEffect.WATER_DROP.display(tLoc, 10, 0.5, 0.5, 0.5, 0);}
                    }

      if (!finalDrawDone && duration - (System.currentTimeMillis() - chargeEndTime) <= 4000) {drawThorns(); finalDrawDone = true;} //if less than 4 seconds remain, draw the last wave of particles

      tickDamage();

    } else { //while charging / holding the charge

      //check if move should end:

      //end move if they change slots or get chiblocked
      if (!bPlayer.canBend(this)) {remove(); return;}

      //end move if player wanders too far from move position WHILE CHARGING IT
      if (player.getLocation().distanceSquared(location) > wanderLimit) {remove(); return;}

      //end move if they stop sneaking before charge is done
      if (!player.isSneaking() && System.currentTimeMillis()-this.getStartTime() < chargeTime) {remove(); return;}

      //display particles:

      //if move is not charged, show green particles
      if (System.currentTimeMillis()-this.getStartTime() < chargeTime) {
        ParticleEffect.COMPOSTER.display(player.getLocation().add(0,1,0), 1, 0.5, 0.5, 0.5, 0); //charge particles
      }

      //if move IS charged, show crit particles
      else { 
        ParticleEffect.CRIT.display(player.getLocation().add(0,1,0), 1, 0.5, 0.5, 0.5, 0); //charge particles
      }
    }

    if (System.currentTimeMillis()-chargeEndTime >= duration && charged) { //End of move
      //end move
      remove();
      return;
    }

  }

  public boolean prepare() {
    location = player.getLocation();

    Block block = location.getBlock();
    if (block.isLiquid() || !isTransparent(block)) { //check if not standing in clear space
      return false;
    }

    if (!checkVegetation()) { //if not enough plants, indicate failure to player
      player.getWorld().playSound(player.getLocation(),Sound.BLOCK_LAVA_EXTINGUISH, 0.5f, 2f);
      ParticleEffect.SMOKE_NORMAL.display(player.getLocation().add(0,1,0), 10, 0.5, 0.5, 0.5, 0);
      return false;
    }

    return true;
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "ThornRows";
  }

  @Override
  public String getDescription() {
    return "This ability allows plantbenders to cover the ground around them with sharp thorns and brambles, hurting anyone else who steps on them - though others may try to control the thorns themselves.";
  }

  @Override
  public String getInstructions() {
    return "Hold shift when surrounded by thick foliage to charge up, then release shift after a moment to produce thorns and brambles in the ground around you. The thickness of the brambles depends on the amount of plants that were in the area.";
  }

  @Override
	public boolean isSneakAbility() {
		return true;
	}
  
  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "Thel";
  }

  @Override
  public String getVersion() {
    return "v1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new ThornRowsListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.Cooldown", 20000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.Radius", 10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.Duration", 10000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.DensityFactor", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.VegetationThreshold",0.2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.ChargeTime",2000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.HurtBoxHeight",2.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.Damage",0.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.PlantBreakChance",0.2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.WanderLimit",10);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.HitDistance",0.5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Thel.Water.Plant.ThornRows.DamageCooldown",750);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
  }

  @Override
  public void remove() {
    removeThorns();
    super.remove();
  }

}
