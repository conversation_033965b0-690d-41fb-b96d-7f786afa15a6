package com.thelastblockbender.thornrows;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.block.Action;

import org.bukkit.event.player.PlayerToggleSneakEvent;


public class ThornRowsListener implements org.bukkit.event.Listener {
  @EventHandler(ignoreCancelled = true)
  public void onSneak(PlayerToggleSneakEvent event) { //on sneak:

    if (!event.isSneaking()) {
        return;
    }

    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer()); //ensure player is valid
    if (bPlayer == null) {return;}

    String abilityName = bPlayer.getBoundAbilityName(); //check selected ability is correct
    if (abilityName.equalsIgnoreCase("ThornRows")) {
      new ThornRows(event.getPlayer()); //start move
    }
  }
}
