package com.thelastblockbender.leafshield;

import java.util.EnumSet;
import java.util.HashSet;
import java.util.Set;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class LeafShield extends PlantAbility implements AddonAbility {
  private final Set<TempBlock> shield = new HashSet<>();
  private int radius;
  private long cooldown;
  private long duration;

  public LeafShield(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, LeafShield.class)) {
      return;
    }

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.duration = TLBMethods.getInt("Abilities.Plantbending.LeafShield.Duration", currentLevel);
    this.cooldown = TLBMethods.getInt("Abilities.Plantbending.LeafShield.Cooldown", currentLevel);
    this.radius = TLBMethods.getInt("Abilities.Plantbending.LeafShield.Radius", currentLevel);

    start();
  }

  @Override
  public void progress() {
    if (!bPlayer.canBend(this) || !player.isSneaking()) {
      remove();
      return;
    }
    if (System.currentTimeMillis() > this.getStartTime() + this.duration) {
      remove();
      return;
    }
    Block block = getGround();
    if (!isGroundPlant(block)) {
      remove();
      return;
    }
    revertLeaves();

    Vector direction = player.getEyeLocation().getDirection().clone();
    Location center = player.getEyeLocation().clone().add(direction.normalize().multiply(radius + 1));
    shield.add(new TempBlock(center.getBlock(), Material.OAK_LEAVES));

    for (int i = 1; i <= radius; i++) {
      double step = 40 / (double) i;
      for (double angle = 0; angle < 360; angle += step) {
        Vector ortho = GeneralMethods.getOrthogonalVector(direction, angle, i);
        Block b = center.clone().add(ortho).getBlock();
        if (isAir(b.getType()) || b.isPassable()) {
          if (!TempBlock.isTempBlock(b)) {
            shield.add(new TempBlock(b, Material.OAK_LEAVES));
          }
        }
      }
    }
  }

  private Block getGround() {
    Block standingblock = player.getLocation().getBlock();
    for (int i = 0; i <= Math.max(radius, 5); i++) {
      Block block = standingblock.getRelative(BlockFace.DOWN, i);
      if (GeneralMethods.isSolid(block) || block.isLiquid()) {
        return block;
      }
    }
    return null;
  }

  private void revertLeaves() {
    for (TempBlock tb : shield) {
      if (tb.getBlock().getType() == Material.OAK_LEAVES) {
        tb.revertBlock();
      }
    }
    shield.clear();
  }

  @Override
  public void remove() {
    super.remove();
    if (!shield.isEmpty()) {
      bPlayer.addCooldown(this);
      revertLeaves();
    }
  }

  @Override
  public long getCooldown() {
    return this.cooldown;
  }

  @Override
  public Location getLocation() {
    return null;
  }

  @Override
  public String getName() {
    return "LeafShield";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public String getInstructions() {
    return "Hold shift to form a shield made of roots, vines and other plants."
      + "Make sure you are standing on a soft surface so you can pull roots and plants from the ground."
      + "(Sand, Dirt, Grass, etc).";
  }

  @Override
  public String getDescription() {
    return "Manipulate plants into a shield to defend yourself!";
  }

  @Override
  public boolean isEnabled() {
    return true;
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new LeafShieldListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafShield.Cooldown", 2000);
    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafShield.Radius", 2);
    ConfigManager.getConfig().addDefault("Abilities.Plantbending.LeafShield.Duration", 1000);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " stopped! ");
    super.remove();
  }
}
