package com.thelastblockbender.leafshield;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class LeafShieldListener implements Listener {

  @EventHandler
  public void onSneak(PlayerToggleSneakEvent event) {
    if (event.isCancelled() || !event.isSneaking()) {
      return;
    }
    Player player = event.getPlayer();
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("LeafShield")) {
      new LeafShield(player);
    }
  }
}
