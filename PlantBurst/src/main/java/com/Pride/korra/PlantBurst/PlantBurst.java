package com.Pride.korra.PlantBurst;

import org.bukkit.plugin.Plugin;
import org.bukkit.event.Listener;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;

import java.util.Iterator;
import com.projectkorra.projectkorra.util.DamageHandler;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Entity;
import com.projectkorra.projectkorra.util.ParticleEffect;
import org.bukkit.util.Vector;
import org.bukkit.Sound;
import org.bukkit.Material;
import com.projectkorra.projectkorra.waterbending.plant.PlantRegrowth;
import java.util.List;
import com.projectkorra.projectkorra.Manager;
import com.projectkorra.projectkorra.util.StatisticsManager;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.configuration.file.FileConfiguration;
import com.projectkorra.projectkorra.GeneralMethods;
import java.util.Set;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.ability.Ability;
import com.projectkorra.projectkorra.ability.CoreAbility;
import org.bukkit.entity.Player;
import java.util.Random;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.block.Block;
import org.bukkit.Location;
import java.util.ArrayList;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.PlantAbility;

public class PlantBurst extends PlantAbility implements AddonAbility
{
    private static String path;
    private ArrayList<Location> locations;
    private long cooldown;
    private double damage;
    private boolean selected;
    private long revertTime;
    private boolean ready;
    private double selectRange;
    private double radius;
    private double yaw;
    private double size;
    private Block sourceBlock;
    private Location sourceLoc;
    private TempBlock grabTemp;
    private TempBlock temp;
    Random rand;
    private long currentLevel;
    
    static {
        PlantBurst.path = "ExtraAbilities.Prride.PlantBurst.";
    }
    
    public PlantBurst(final Player player) {
        super(player);
        this.locations = new ArrayList<Location>();
        this.rand = new Random();
        if (!this.bPlayer.canBend((CoreAbility)this)) {
            return;
        }
        if (this.bPlayer.isOnCooldown((CoreAbility)this)) {
            this.remove();
            return;
        }
        
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);
        
        this.cooldown = TLBMethods.getLong(String.valueOf(PlantBurst.path) + "Cooldown", currentLevel);
        this.damage = TLBMethods.getDouble(String.valueOf(PlantBurst.path) + "Damage", currentLevel);
        this.revertTime = TLBMethods.getLong(String.valueOf(PlantBurst.path) + "RevertTime", currentLevel);
        this.selectRange = TLBMethods.getDouble(String.valueOf(PlantBurst.path) + "SelectRange", currentLevel);
        this.radius = TLBMethods.getDouble(String.valueOf(PlantBurst.path) + "Radius", currentLevel);
        this.sourceBlock = player.getTargetBlock((Set)null, (int)this.selectRange);
        if (this.sourceBlock != null && !GeneralMethods.isRegionProtectedFromBuild((Ability)this, this.sourceBlock.getLocation()) && isPlant(this.sourceBlock)) {
            this.sourceLoc = this.sourceBlock.getLocation();
            this.selected = true;
            this.start();
        }
    }

    
    public long getCooldown() {
        return this.cooldown;
    }
    
    public Location getLocation() {
        return null;
    }
    
    public String getName() {
        return "PlantBurst";
    }
    
    public boolean isHarmlessAbility() {
        return false;
    }
    
    public boolean isSneakAbility() {
        return true;
    }
    
    public void progress() {
        if (this.selected) {
            if (this.player.isSneaking()) {
                if (isPlant(this.sourceBlock)) {
                    new PlantRegrowth(this.player, this.sourceBlock);
                    this.sourceBlock.setType(Material.AIR);
                }
                final Vector grabDir = GeneralMethods.getDirection(this.sourceLoc, this.player.getLocation().add(0.0, 1.0, 0.0));
                grabDir.normalize().multiply(1);
                this.sourceLoc.add(grabDir);
                (this.grabTemp = new TempBlock(this.sourceLoc.getBlock(), Material.SHORT_GRASS)).setRevertTime(4000L);
                if (this.rand.nextInt(3) == 0) {
                    this.sourceLoc.getWorld().playSound(this.sourceLoc, Sound.BLOCK_GRASS_BREAK, 1.0f, 0.0f);
                }
                if (this.sourceLoc.distanceSquared(this.player.getLocation().add(0.0, 1.0, 0.0)) < 0.36) {
                    (this.temp = new TempBlock(this.player.getLocation().add(0.0, 1.0, 0.0).getBlock(), Material.SHORT_GRASS)).setRevertTime(this.revertTime);
                    this.ready = true;
                }
            }
            else {
                if (!this.ready) {
                    this.remove();
                    return;
                }
                this.sourceLoc = this.player.getEyeLocation();
                this.plantBurst();
                this.bPlayer.addCooldown((Ability)this);
            }
        }
    }
    
    private void plantBurst() {
    	
        this.size += 0.3;
        final Location baseLoc = this.player.getLocation().clone().add(0.0, this.size / 2.0, 0.0);
        final Location loc = baseLoc.clone();
        loc.setPitch(0.0f);
        final Location location = loc;
        final double yaw = this.yaw + 90.0;
        this.yaw = yaw;
        location.setYaw((float)yaw);
        final Vector direction = loc.getDirection();
        for (double j = -180.0; j <= 180.0; j += 45.0) {
            final Location tempLoc = loc.clone();
            final Vector newDir = direction.clone().multiply(this.size * Math.cos(Math.toRadians(j)));
            tempLoc.add(newDir);
            tempLoc.setY(tempLoc.getY() + this.size * Math.sin(Math.toRadians(j)));
            this.locations.add(tempLoc);
            if (!GeneralMethods.isSolid(tempLoc.getBlock())) {
                ParticleEffect.BLOCK_CRACK.display(tempLoc, 10, 0.0, 0.0, 0.0, 0.0, (Object)Material.OAK_LEAVES.createBlockData());
                (this.temp = new TempBlock(tempLoc.getBlock(), Material.SHORT_GRASS)).setRevertTime(2500L);
                for (final Entity entity : GeneralMethods.getEntitiesAroundPoint(tempLoc, 2.0)) {
                    if (entity.getUniqueId() != this.player.getUniqueId() && entity instanceof LivingEntity) {
                        DamageHandler.damageEntity(entity, this.damage, (Ability)this);
                    }
                }
            }
        }
        if (this.size >= this.radius) {
            this.bPlayer.addCooldown((Ability)this);
            this.remove();
        }
    }
    
    public String getAuthor() {
        return "Prride & Shookified";
    }
    
    public String getVersion() {
        return "PlantBurst V1.1";
    }
    
    public String getDescription() {
        return "PlantBurst is an advanced technique used by plantbenders to take in a large quantity of plants and release them as a burst of plants.";
    }
    
    public String getInstructions() {
        return "Hold sneak and release when the plants reach you.";
    }
    
    public void load() {
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents((Listener)new PlantBurstListener(), (Plugin)ProjectKorra.plugin);
        ProjectKorra.log.info(String.valueOf(this.getName()) + " " + this.getVersion() + " by " + this.getAuthor() + " loaded! ");
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "Cooldown", (Object)6500);
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "Damage", (Object)3);
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "GrabSpeed", (Object)0.3);
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "RevertTime", (Object)5000);
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "SelectRange", (Object)20);
        ConfigManager.getConfig().addDefault(String.valueOf(PlantBurst.path) + "Radius", (Object)10);
        ConfigManager.defaultConfig.save();
    }
    
    public void stop() {
    }
}
