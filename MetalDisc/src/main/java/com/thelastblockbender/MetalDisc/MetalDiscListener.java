package com.thelastblockbender.MetalDisc;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class MetalDiscListener implements Listener {

    @EventHandler
    public void onSneak(PlayerToggleSneakEvent event) {
        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
        
        if (event.isSneaking() && bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("MetalDisc")) {
            // Check if the ability isn't already active
            if (CoreAbility.getAbility(player, MetalDisc.class) == null) {
                new MetalDisc(player);
            }
        }
    }

    @EventHandler
    public void onSwing(final PlayerAnimationEvent event) {
        Player player = event.getPlayer();
        BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
        
        // Only allow throwing if player is still on the MetalDisc slot
        if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("MetalDisc")) {
            MetalDisc metalDisc = CoreAbility.getAbility(player, MetalDisc.class);
            
            if (metalDisc != null && !metalDisc.isThrown()) {
                metalDisc.onLeftClick();
            }
        }
    }
}
