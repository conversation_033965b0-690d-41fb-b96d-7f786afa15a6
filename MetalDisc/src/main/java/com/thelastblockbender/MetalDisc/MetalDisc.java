package com.thelastblockbender.MetalDisc;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Item;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.MetalAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class MetalDisc extends MetalAbility implements AddonAbility {

    // Configuration variables
    private long ironCooldown;
    private long goldCooldown;
    private double spawnDistance;
    private long despawnDelay;
    private double maxDistance;
    private double speed;
    private double ironDamage;
    private double goldDamage;
    private double goldKnockback;
    
    // Runtime variables
    private ItemDisplay disc;
    private Material nuggetType;
    private boolean thrown;
    private Location spawnLocation;
    private Vector direction;
    private double arcHeight;
    private Item droppedNugget;

    public MetalDisc(Player player) {
        super(player);
        
        if (!bPlayer.canBend(this)) {
            return;
        }



        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        long currentLevel = TLBMethods.limitLevels(player, statLevel);
            // Load configuration values
        ironCooldown = TLBMethods.getLong("Abilities.Metal.MetalDisc.IronCooldown", currentLevel);
        goldCooldown = TLBMethods.getLong("Abilities.Metal.MetalDisc.GoldCooldown", currentLevel);
        spawnDistance = TLBMethods.getDouble("Abilities.Metal.MetalDisc.SpawnDistance", currentLevel);
        despawnDelay = TLBMethods.getLong("Abilities.Metal.MetalDisc.DespawnDelay", currentLevel);
        maxDistance = TLBMethods.getDouble("Abilities.Metal.MetalDisc.MaxDistance", currentLevel);
        speed = TLBMethods.getDouble("Abilities.Metal.MetalDisc.Speed", currentLevel);
        ironDamage = TLBMethods.getDouble("Abilities.Metal.MetalDisc.IronDamage", currentLevel);
        goldDamage = TLBMethods.getDouble("Abilities.Metal.MetalDisc.GoldDamage", currentLevel);
        goldKnockback = TLBMethods.getDouble("Abilities.Metal.MetalDisc.GoldKnockback", currentLevel);
        arcHeight = TLBMethods.getDouble("Abilities.Metal.MetalDisc.ArcHeight", currentLevel);

        // Check for required nuggets - prioritize held item
        ItemStack heldItem = player.getInventory().getItemInOffHand();
        if (heldItem.getType() != Material.IRON_NUGGET || heldItem.getType() != Material.GOLD_NUGGET ) {
            heldItem = player.getInventory().getItemInOffHand();
        }


        if (heldItem.getType() == Material.IRON_NUGGET) {
            nuggetType = Material.IRON_NUGGET;
        } else if (heldItem.getType() == Material.GOLD_NUGGET) {
            nuggetType = Material.GOLD_NUGGET;
        } else if (player.getInventory().contains(Material.IRON_NUGGET)) {
            nuggetType = Material.IRON_NUGGET;
        } else if (player.getInventory().contains(Material.GOLD_NUGGET)) {
            nuggetType = Material.GOLD_NUGGET;
        } else {
            return;
        }
       // Remove and "throw" the nugget
       player.getInventory().removeItem(new ItemStack(nuggetType, 1));
        
       // Calculate where the disc will spawn
       Location nuggetLoc = player.getEyeLocation().add(player.getEyeLocation().getDirection().multiply(spawnDistance));
       nuggetLoc.setY(player.getLocation().getY());  // Set to ground level
       
       // Drop and configure the nugget
       droppedNugget = player.getWorld().dropItem(player.getLocation(), new ItemStack(nuggetType, 1));
       droppedNugget.setPickupDelay(Integer.MAX_VALUE); // Prevent pickup
       droppedNugget.setVelocity(player.getLocation().getDirection().multiply(0.75));
       
       createDisc();
        start();
    }


    private void createDisc() {
        // Calculate final position
        spawnLocation = player.getEyeLocation().add(player.getEyeLocation().getDirection().multiply(spawnDistance));
        
        // Start from ground position directly below the spawn location
        Location startLocation = spawnLocation.clone();
        startLocation.setY(player.getLocation().getY());  // Set to player's ground level
        
        disc = player.getWorld().spawn(startLocation, ItemDisplay.class);
        disc.setGravity(false);
        disc.setItemStack(new ItemStack(nuggetType == Material.IRON_NUGGET ?
            Material.HEAVY_WEIGHTED_PRESSURE_PLATE : Material.LIGHT_WEIGHTED_PRESSURE_PLATE));

        // Play anvil sound when disc is formed
        player.getWorld().playSound(disc.getLocation(), Sound.BLOCK_ANVIL_USE, 0.8f, 1.2f);

        // Animate the disc rising straight up
        final int steps = 10;
        double heightDifference = spawnLocation.getY() - startLocation.getY();
        final double stepHeight = heightDifference / steps;
        
        for (int i = 0; i < steps; i++) {
            final int step = i;
            org.bukkit.Bukkit.getScheduler().runTaskLater(ProjectKorra.plugin, () -> {
                Location newLoc = startLocation.clone();
                newLoc.add(0, stepHeight * step, 0);  // Only move vertically
                disc.teleport(newLoc);
                
                // Add rising particles
                ParticleEffect.BLOCK_CRACK.display(newLoc, 3, 0.1, 0.1, 0.1, 
                    (nuggetType == Material.IRON_NUGGET ? 
                        Material.IRON_BLOCK : Material.GOLD_BLOCK).createBlockData());
            }, (long) (i * 0.5));
        }

        this.droppedNugget.remove();
        
    
        // Schedule despawn if not thrown
        org.bukkit.Bukkit.getScheduler().runTaskLater(ProjectKorra.plugin, () -> {
            if (!thrown) {
                remove();
            }
        }, despawnDelay);
    }

    @Override
    public void progress() {
        if (!player.isOnline() || player.isDead()) {
            remove();
            return;
        }

        if (!thrown) {
            // Check distance from player
            if (disc.getLocation().distance(player.getLocation()) > maxDistance) {
                remove();
                return;
            }
            
        }

        if (thrown) {
            // Handle thrown disc
            direction = player.getEyeLocation().getDirection();
            Vector velocity = direction.multiply(speed);
            velocity.setY(velocity.getY() + arcHeight); // Add slight arc
            
            Location newLoc = disc.getLocation().add(velocity);
            
            // Check if the disc would hit a solid block
            if (GeneralMethods.isSolid(newLoc.getBlock()) || !isTransparent(player, newLoc.getBlock())) {
                // Play impact effect
                ParticleEffect.BLOCK_CRACK.display(newLoc, 8, 0.3, 0.3, 0.3, 
                    (nuggetType == Material.IRON_NUGGET ? 
                        Material.IRON_BLOCK : Material.GOLD_BLOCK).createBlockData());
                newLoc.getWorld().playSound(newLoc, Sound.BLOCK_METAL_HIT, 1.0f, 1.0f);
                bPlayer.addCooldown(this);
                remove();
                return;
            }
            
            disc.teleport(newLoc);

            // Check for collision with entities
            for (Entity entity : GeneralMethods.getEntitiesAroundPoint(newLoc, 1.5)) {
                if (entity instanceof Player && entity.getUniqueId().equals(player.getUniqueId())) 
                    continue;
                
                if (entity instanceof LivingEntity) {
                    affectEntity((LivingEntity) entity);
                    remove();
                    return;
                }
            }
        }

        // Remove if too far
        if (disc.getLocation().distance(spawnLocation) > maxDistance) {
            bPlayer.addCooldown(this);
            remove();
        }
        
    }

    private void affectEntity(LivingEntity entity) {
        if (nuggetType == Material.IRON_NUGGET) {
            DamageHandler.damageEntity(entity, ironDamage, this);
        } else {
            DamageHandler.damageEntity(entity, goldDamage, this);
            Vector kb = direction.clone().setY(0.2).normalize().multiply(goldKnockback);
            entity.setVelocity(kb);
        }
        ParticleEffect.BLOCK_CRACK.display(entity.getLocation(), 10, 0.5, 0.5, 0.5, 
            (nuggetType == Material.IRON_NUGGET ? Material.IRON_BLOCK : Material.GOLD_BLOCK).createBlockData());
    }

    public void onLeftClick() {
        if (!thrown && disc != null) {
            thrown = true;
            bPlayer.addCooldown(this);
        }
    }

	public boolean isThrown() {
        return thrown;
    }

    @Override
    public void remove() {
        super.remove();
        
        this.droppedNugget.remove();
        if (disc != null) {
            disc.remove();
        }
    }

    // Addon methods
    @Override
    public String getAuthor() {
        return "TLB";
    }

    @Override
    public String getVersion() {
        return "1.0";
    }

    @Override
    public String getDescription() {
        return "Throw metal discs made from iron or golden nuggets to attack enemies!";
    }

    @Override
    public String getInstructions() {
        return "Sneak while holding a metal nugget (iron or gold) and wait for the disc to appear and punch to throw. Iron discs deal more damage while gold discs deal less damage but knockback enemies.";
    }

    @Override
    public void load() {

    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new MetalDiscListener(), ProjectKorra.plugin);

    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new MetalDiscListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.IronCooldown", 3000);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.GoldCooldown", 3000);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.SpawnDistance", 3.0);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.DespawnDelay", 4000);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.MaxDistance", 30.0);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.Speed", 0.8);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.IronDamage", 4.0);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.GoldDamage", 2.0);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.GoldKnockback", 1.5);
    ConfigManager.getConfig().addDefault("Abilities.Metal.MetalDisc.ArcHeight", 0.13);

    ConfigManager.defaultConfig.save();
    }

    @Override
    public void stop() {}

    // Ability methods
    @Override
    public long getCooldown() {
        return nuggetType == Material.IRON_NUGGET ? ironCooldown : goldCooldown;
    }

    @Override
    public Location getLocation() {
        return player.getLocation();
    }

    @Override
    public String getName() {
        return "MetalDisc";
    }

    @Override
    public boolean isHarmlessAbility() {
        return false;
    }

    @Override
    public boolean isSneakAbility() {
        return true;
    }

}
