package me.simplicitee.fartbending.fartblast;

import java.util.Iterator;

import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.AirAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class FartBlast extends AirAbility implements AddonAbility {
   private long cooldown;
   private long currentLevel;
   private long radius;
   private Long fartPower;
   private int fartPotency;

   public FartBlast(Player player) {
      super(player);
      int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
      this.currentLevel = TLBMethods.limitLevels(player, statLevel);
      this.radius = TLBMethods.getLong("Abilities.Air.FartBlast.Radius", this.currentLevel);
      this.cooldown = TLBMethods.getLong("Abilities.Air.FartBlast.Cooldown", this.currentLevel);
      this.fartPower = TLBMethods.getLong("Abilities.Air.FartBlast.FartPower", this.currentLevel);
      this.fartPotency = TLBMethods.getInt("Abilities.Air.FartBlast.FartPotency", this.currentLevel);
      this.start();
   }

   public void progress() {
      Location loc = this.getLocation();
      this.playFartParticles(loc, 4, 0.7D);
      Iterator var3 = GeneralMethods.getEntitiesAroundPoint(loc.add(0.0D, -1.0D, 0.0D), (double)this.radius).iterator();

      while(var3.hasNext()) {
         Entity e = (Entity)var3.next();
         if (e instanceof Player && e.getEntityId() != this.player.getEntityId()) {
            ((Player)e).addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, this.fartPotency, 2));
            System.out.println("ew");
         }
      }

      this.player.setVelocity(this.player.getLocation().getDirection().setY(0.1D).multiply((float)this.fartPower));
      this.bPlayer.addCooldown("Flatulence", this.cooldown);
      this.remove();
   }

   public boolean isSneakAbility() {
      return true;
   }

   public boolean isHarmlessAbility() {
      return false;
   }

   public boolean isIgniteAbility() {
      return false;
   }

   public boolean isExplosiveAbility() {
      return false;
   }

   public long getCooldown() {
      return this.cooldown;
   }

   public String getName() {
      return "Flatulence";
   }

   public Location getLocation() {
      Vector dir = this.player.getLocation().getDirection().multiply(-0.2D).setY(-0.02D);
      return this.player.getLocation().add(0.0D, 0.9D, 0.0D).add(dir);
   }

   public String getDescription() {
      return "Get creative with your air bending and bend the air from a whole new angle!";
   }

   public String getInstructions() {
      return "Sneak to relieve some pressure (may cause nausea)";
   }

   public void playFartParticles(Location loc, int amount, double offsets) {
      // Create realistic puff animation - air expelled downwards and outwards

      // Initial burst from the source
      for (int i = 0; i < 15; i++) {
         // Create random directions that favor downward and outward movement
         double angle = Math.random() * 2 * Math.PI; // Random horizontal angle
         double verticalAngle = Math.random() * Math.PI * 0.7 + Math.PI * 0.15; // Mostly downward (15-85 degrees down)

         // Calculate velocity components
         double horizontalDistance = Math.sin(verticalAngle) * (0.5 + Math.random() * 1.5);
         double verticalDistance = -Math.cos(verticalAngle) * (0.3 + Math.random() * 0.8); // Negative for downward

         double x = horizontalDistance * Math.cos(angle);
         double z = horizontalDistance * Math.sin(angle);

         Location particleLoc = loc.clone().add(x, verticalDistance, z);

         // Use cloud particles for the main puff effect
         ParticleEffect.CLOUD.display(particleLoc, 1, 0.1, 0.1, 0.1, 0.02);

         // Add some green-tinted dust particles for color
         if (Math.random() > 0.7) {
            ParticleEffect.REDSTONE.display(particleLoc, 1, 0.05, 0.05, 0.05, 0,
               new org.bukkit.Particle.DustOptions(org.bukkit.Color.fromRGB(120, 180, 60), 0.8F));
         }
      }

      // Secondary expanding cloud effect that spreads outward and settles down
      for (int ring = 1; ring <= 3; ring++) {
         double ringRadius = ring * 0.4;
         for (int angle = 0; angle < 360; angle += 30) {
            double x = ringRadius * Math.cos(Math.toRadians(angle));
            double z = ringRadius * Math.sin(Math.toRadians(angle));
            double y = -0.2 - (ring * 0.1); // Each ring is lower than the previous

            Location particleLoc = loc.clone().add(x, y, z);

            // Lighter cloud particles for the expanding effect
            if (Math.random() > 0.3) {
               ParticleEffect.CLOUD.display(particleLoc, 1, 0.15, 0.05, 0.15, 0.01);
            }
         }
      }

      // Add some sneeze particles for extra texture
      for (int i = 0; i < 8; i++) {
         double x = (Math.random() - 0.5) * 1.2;
         double z = (Math.random() - 0.5) * 1.2;
         double y = -0.1 - Math.random() * 0.4;

         Location particleLoc = loc.clone().add(x, y, z);
         ParticleEffect.SNEEZE.display(particleLoc, 1, 0.1, 0.1, 0.1);
      }
   }

   public void load() {
      ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new FartListener(), ProjectKorra.plugin);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.Cooldown", 5000);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.Radius", 0.4D);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.FartPower", 1.2D);
      ConfigManager.getConfig().addDefault("Abilities.Air.FartBlast.FartPotency", 100);
      ConfigManager.defaultConfig.save();
   }

   public void stop() {
   }

   public String getAuthor() {
      return null;
   }

   public String getVersion() {
      return null;
   }
}
