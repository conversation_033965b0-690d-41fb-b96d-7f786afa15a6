package me.simplicitee.fartbending.fartblast;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;

public class <PERSON>t<PERSON>istener implements Listener {
   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void onSneak(PlayerToggleSneakEvent event) {
      if (!event.isCancelled()) {
         if (!event.getPlayer().isSneaking()) {
            BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer.getBoundAbilityName().equals("Flatulence") && !bPlayer.isOnCooldown("Flatulence")) {
               new FartBlast(event.getPlayer());
            }

         }
      }
   }
}
