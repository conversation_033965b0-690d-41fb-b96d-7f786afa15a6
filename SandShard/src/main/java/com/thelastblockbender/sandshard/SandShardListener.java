package com.thelastblockbender.sandshard;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class SandShardListener implements Listener {
  @EventHandler
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    }
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("SandShard")) {
      new SandShard(event.getPlayer());
    }
  }
}
