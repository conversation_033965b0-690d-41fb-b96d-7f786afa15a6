package com.thelastblockbender.sandshard;

import java.util.EnumSet;
import java.util.Random;
import java.util.Set;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.SandAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class SandShard extends SandAbility implements AddonAbility {
  private static final Set<Material> SAND = EnumSet.of(Material.SAND, Material.SANDSTONE,
    Material.SANDSTONE_SLAB, Material.SANDSTONE_STAIRS, Material.SANDSTONE_WALL,
    Material.SMOOTH_SANDSTONE, Material.CUT_SANDSTONE, Material.CHISELED_SANDSTONE);

  private static final Set<Material> RED_SAND = EnumSet.of(Material.RED_SAND, Material.RED_SANDSTONE,
    Material.RED_SANDSTONE_SLAB, Material.RED_SANDSTONE_STAIRS, Material.RED_SANDSTONE_WALL,
    Material.SMOOTH_RED_SANDSTONE, Material.CUT_RED_SANDSTONE, Material.CHISELED_RED_SANDSTONE);

  // Configurable variables;
  private long cooldown;
  private double range;
  private double speed;
  private double damage;
  private double collisionRadius;
  private double push;

  // Instance related variables
  private Location origin;
  private Location location;
  private Vector direction;
  private Block topBlock;

  public SandShard(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, SandShard.class)) {
      return;
    }
    // Allows the sand bender to use SandShard from higher distances (added for sandspout users)
    topBlock = GeneralMethods.getTopBlock(player.getLocation(), -50);

    if (topBlock == null) {
      topBlock = player.getLocation().subtract(0, 1, 0).getBlock();
    }

    if (!validBlock(topBlock)) {
      return;
    }

    setFields();
    start();
  }

  private boolean validBlock(Block block) {
    if (block == null) {
      return false;
    }
    if (!SAND.contains(block.getType()) && !RED_SAND.contains(block.getType())) {
      return false;
    }
    return !GeneralMethods.isRegionProtectedFromBuild(this, player.getLocation());
  }

  private void setFields() {
    this.collisionRadius = ConfigManager.getConfig().getDouble("ExtraAbilities.Earth.SandShard.CollisionRadius");
    this.cooldown = ConfigManager.getConfig().getLong("ExtraAbilities.Earth.SandShard.Cooldown");
    this.range = ConfigManager.getConfig().getDouble("ExtraAbilities.Earth.SandShard.Range");
    this.speed = ConfigManager.getConfig().getDouble("ExtraAbilities.Earth.SandShard.Speed");
    this.damage = ConfigManager.getConfig().getDouble("ExtraAbilities.Earth.SandShard.Damage");
    push = ConfigManager.getConfig().getDouble("ExtraAbilities.Earth.SandShard.Push");
    this.origin = player.getEyeLocation().clone();
    this.location = origin.clone();
    this.direction = player.getEyeLocation().getDirection();

    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());

    long currentLevel = TLBMethods.limitLevels(player, statLevel);
    this.range = (int) (currentLevel + 5);
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreBinds(this)) {
      remove();
      return;
    }
    if (origin.distanceSquared(location) > range * range) {
      remove();
      bPlayer.addCooldown(this);
      return;
    }

    location.add(direction.clone().multiply(speed));
    if (!isTransparent(player, location.getBlock()) && !TempBlock.isTempBlock(location.getBlock())) {
      bPlayer.addCooldown(this);
      remove();
      return;
    }
    if (GeneralMethods.isRegionProtectedFromBuild(player, "SandShard", location)) {
      remove();
      return;
    }
    displayProjectile();

    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(location, collisionRadius)) {
      if (entity instanceof LivingEntity && entity.getUniqueId() != player.getUniqueId()) {
        DamageHandler.damageEntity(entity, damage, this);
        entity.setVelocity(player.getEyeLocation().getDirection().normalize().multiply(push));
        remove();
        bPlayer.addCooldown(this);
        return;
      }
    }

    if (new Random().nextInt(5) == 0) {
      playSandbendingSound(location);
    }
  }

  private void displayProjectile() {
    if (RED_SAND.contains(topBlock.getType())) {
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      GeneralMethods.displayColoredParticle("ba6d02", location, 1, 0.1, 0.1, 0.1);
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      GeneralMethods.displayColoredParticle("784212", location, 1, 0.1, 0.1, 0.1);
    } else {
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      GeneralMethods.displayColoredParticle("9e9446", location, 1, 0.1, 0.1, 0.1);
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      ParticleEffect.BLOCK_CRACK.display(location, 2, 1, 1, 1, topBlock.getBlockData());
      GeneralMethods.displayColoredParticle("b59f73", location, 1, 0.1, 0.1, 0.1);
    }
  }

  @Override
  public void remove() {
    super.remove();
    if (TempBlock.isTempBlock(location.getBlock())) {
      TempBlock.removeBlock(location.getBlock());
      TempBlock.revertBlock(location.getBlock(), Material.AIR);
    }
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "SandShard";
  }

  @Override
  public String getDescription() {
    return "This move allows a sandbender to propell grains of sand at a high speed, damaging their opponents.";

  }

  @Override
  public String getInstructions() {
    return "While standing on a sand source, left click to send grains of sand speeding towards your target!";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SandShardListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.CollisionRadius", 1.3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.Cooldown", 2000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.Range", 15);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.Speed", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.Damage", 5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Earth.SandShard.Push", 1.2);
    ConfigManager.defaultConfig.save();

  }

  @Override
  public void stop() {
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " unloaded!");
    super.remove();
  }
}
