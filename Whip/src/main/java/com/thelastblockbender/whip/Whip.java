package com.thelastblockbender.whip;

import java.util.EnumSet;
import java.util.Set;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

import com.projectkorra.projectkorra.Element;
import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.WaterAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.BlockSource;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import com.projectkorra.projectkorra.waterbending.util.WaterReturn;

public class Whip extends WaterAbility implements AddonAbility {
	enum Type { NORMAL, ICE, PLANT, SNOW }

	private static final Set<Material> MUSHROOMS = EnumSet.of(
		Material.RED_MUSHROOM, Material.RED_MUSHROOM_BLOCK,
		Material.BROWN_MUSHROOM, Material.BROWN_MUSHROOM_BLOCK, Material.MUSHROOM_STEM
	);

	private long cooldown;
	private double range;
	private double damage;
	private double speed;
	private Location location;
	private Block sourceBlock;
	private Material sourceMat;
	private Type type;
	private double knockback;
	private double knockup;

	public Whip(Player player) {
		super(player);

		if (!player.isSneaking() || !bPlayer.canBend(this) || hasAbility(player, Whip.class)) {
			return;
		}

		setFields();

		if (prepare()) {
			start();
		}
	}

	public void setFields() {
		int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
		long currentLevel = TLBMethods.limitLevels(player, statLevel);

		cooldown = TLBMethods.getLong("ExtraAbilities.NickC1211.Whip.Cooldown", currentLevel);
		range = TLBMethods.getDouble("ExtraAbilities.NickC1211.Whip.Range", currentLevel);
		damage = TLBMethods.getDouble("ExtraAbilities.NickC1211.Whip.Damage", currentLevel);
		speed = TLBMethods.getDouble("ExtraAbilities.NickC1211.Whip.Speed", currentLevel);
		knockback = TLBMethods.getDouble("ExtraAbilities.NickC1211.Whip.Knockback", currentLevel);
		knockup = TLBMethods.getInt("ExtraAbilities.NickC1211.Whip.Knockup", currentLevel);
	}

	private boolean prepare() {
		if (!WaterReturn.hasWaterBottle(player)) {
			boolean allowIce = bPlayer.canUseSubElement(Element.SubElement.ICE);
			boolean allowPlant = bPlayer.canUseSubElement(Element.SubElement.PLANT);

			sourceBlock = BlockSource.getWaterSourceBlock(player, 8, ClickType.SHIFT_DOWN, true, allowIce, allowPlant, allowIce, true);
			if (sourceBlock == null) return false;
			location = sourceBlock.getLocation().clone();
		} else {
			sourceBlock = player.getEyeLocation().clone().getBlock();
			location = sourceBlock.getLocation().clone();
			if (sourceBlock.getType() == Material.WATER) {
				location = sourceBlock.getRelative(BlockFace.UP).getLocation().clone();
			}
		}

		if (isSnow(sourceBlock)) {
			type = Type.SNOW;
		} else if (isPlant(sourceBlock)) {
			type = Type.PLANT;
		} else if (isIce(sourceBlock)) {
			type = Type.ICE;
		} else {
			type = Type.NORMAL;
		}

		sourceMat = sourceBlock.getType();

		return true;
	}

	@Override
	public void progress() {
		if (!player.isSneaking() || !bPlayer.canBendIgnoreCooldowns(this)) {
			remove();
			return;
		}

		if (sourceBlock.getLocation().distance(this.location) > range) {
			bPlayer.addCooldown(this);
			remove();
			return;
		}

		Location currentLoc = location.clone();
		Vector direction = player.getEyeLocation().getDirection();
		location.add(direction.multiply(speed));

		if (currentLoc.getBlock().getType() == Material.ICE) {
			for (final Location l : GeneralMethods.getCircle(currentLoc, 3, 4, false, true, 0)) {
				Block block = l.getBlock();
				if (block.getType() != Material.ICE) continue;
				ParticleEffect.BLOCK_CRACK.display(l, 5, 0, 0, 0, 0, Material.PACKED_ICE.createBlockData());
				l.getWorld().playSound(l, Sound.BLOCK_GLASS_BREAK, 2f, 5f);
				if (TempBlock.isTempBlock(block)) {
					TempBlock temp = TempBlock.get(block);
					if (temp.getState().getType() == Material.WATER) {
						temp.revertBlock();
						continue;
					}
				}
				block.setType(Material.AIR);
			}
		}

		if (!currentLoc.getBlock().getType().isAir() && !isSnow(currentLoc.getBlock().getType()) && !isPlant(currentLoc.getBlock().getType()) && !isWater(currentLoc.getBlock().getType())) {
			remove();
			return;
		}

		Material mat;
		switch (type) {
			case ICE:
				playIcebendingSound(location);
				mat = sourceMat == Material.ICE ? Material.ICE : Material.PACKED_ICE;
				break;
			case SNOW:
				location.getWorld().playSound(location, Sound.BLOCK_SNOW_BREAK, 1, 1);
				mat = Material.SNOW_BLOCK;
				break;
			case PLANT:
				if (isLeaves(sourceBlock)) {
					mat = Material.OAK_LEAVES;
					location.getWorld().playSound(location, Sound.BLOCK_GRASS_BREAK, 1, 1);
				} else if (sourceMat == Material.CACTUS) {
					mat = Material.JUNGLE_LEAVES;
					location.getWorld().playSound(location, Sound.BLOCK_GRASS_BREAK, 1, 1);
				} else if (MUSHROOMS.contains(sourceMat)) {
					ParticleEffect.WATER_DROP.display(location, 5, 0.5, 0.5, 0.5, 0);
					playWaterbendingSound(this.location);
					mat = sourceMat.name().contains("RED") ? Material.RED_MUSHROOM_BLOCK : Material.BROWN_MUSHROOM_BLOCK;
				} else {
					mat = Material.TALL_GRASS;
					location.getWorld().playSound(location, Sound.BLOCK_GRASS_BREAK, 1, 1);
				}
				break;
			case NORMAL:
			default:
				ParticleEffect.WATER_DROP.display(location, 5, 0.5, 0.5, 0.5, 0);
				playWaterbendingSound(location);
				mat = Material.WATER;
				break;
		}

		TempBlock tb = new TempBlock(currentLoc.getBlock(), mat);
		tb.setRevertTime(1200);

		if (WaterReturn.hasWaterBottle(player) && sourceBlock.getLocation().distanceSquared(location) > range * range) {
			WaterReturn.emptyWaterBottle(player);
			remove();
			return;
		}

		boolean hit = false;
		for (Entity entity : GeneralMethods.getEntitiesAroundPoint(this.location, 1.5)) {
			if ((entity instanceof LivingEntity) && entity.getEntityId() != player.getEntityId() && !(entity instanceof ArmorStand)) {
				DamageHandler.damageEntity(entity, player, damage, this);
				hit = true;
				Vector velocity = player.getEyeLocation().getDirection().setY(this.knockup).multiply(this.knockback);
				entity.setVelocity(velocity);
				if (type != Type.NORMAL) {
					if (sourceMat == Material.WITHER_ROSE) {
						((LivingEntity) entity).addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 20, 1));
					} else if (MUSHROOMS.contains(sourceMat)) {
						((LivingEntity) entity).addPotionEffect(new PotionEffect(PotionEffectType.NAUSEA, 20, 1));
					} else if (type == Type.ICE || type == Type.PLANT) {
						((LivingEntity) entity).addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 20, 1));
					} else if (type == Type.SNOW) {
						((LivingEntity) entity).addPotionEffect(new PotionEffect(PotionEffectType.BLINDNESS, 20, 1));
					}
				}
			}
		}
		if (hit) {
			remove();
		}
	}

	@Override
	public void remove() {
		bPlayer.addCooldown(this);
		super.remove();
	}

	@Override
	public long getCooldown() {
		return cooldown;
	}

	@Override
	public Location getLocation() {
		return location;
	}

	@Override
	public String getName() {
		return "Whip";
	}

	@Override
	public String getDescription() {
		return "Send a whip of water, plant, ice, or snow at your opponent.";
	}

	@Override
	public String getInstructions() {
		return "Hold Shift and Left Click to shoot. Try different source blocks for different effects!";
	}

	@Override
	public boolean isHarmlessAbility() {
		return false;
	}

	@Override
	public boolean isSneakAbility() {
		return ConfigManager.getConfig().getBoolean("ExtraAbilities.NickC1211.Whip.Swim.Disabled");
	}

	@Override
	public String getAuthor() {
		return "TLB (Original: Nick)";
	}

	@Override
	public String getVersion() {
		return "1.0.0";
	}

	@Override
	public void load() {
		ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new WhipListener(), ProjectKorra.plugin);
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");

		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Cooldown", 2000);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Range", 20);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Damage", 4.0);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Speed", 1.0);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Knockback", 1.0);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Knockup", 0.15);
		ConfigManager.getConfig().addDefault("ExtraAbilities.NickC1211.Whip.Swim.Disabled", Boolean.TRUE);
		ConfigManager.defaultConfig.save();
	}

	@Override
	public void stop() {
		ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " disabled!");
	}
}
