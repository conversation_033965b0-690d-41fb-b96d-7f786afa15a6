package com.thelastblockbender.whip;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class WhipListener implements Listener {
	@EventHandler(ignoreCancelled = true)
	public void onSwing(PlayerAnimationEvent event) {
		if (BendingPlayer.getBendingPlayer(event.getPlayer()).getBoundAbilityName().equalsIgnoreCase("Whip")) {
			new Whip(event.getPlayer());
		}
	}
}
