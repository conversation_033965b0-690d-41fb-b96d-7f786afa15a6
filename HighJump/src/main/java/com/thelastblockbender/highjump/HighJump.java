package com.thelastblockbender.highjump;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class HighJump extends ChiAbility implements AddonAbility {
  private HighJumpType highJumpType;
  private long jumpCooldown;
  private long jumpHeight;
  private long lungeCooldown;
  private long lungeHeight;
  private long evadeCooldown;
  private long evadeHeight;
  private long doubleJumpCooldown;
  private long doubleJumpHeight;
  private long evadeDistance;
  private long lungeDistance;
  private Location location;
  private boolean enableLunge;
  private boolean enableJump;
  private boolean enableDoubleJump;
  private boolean enableEvade;
  private boolean playParticles;
  private float fallDistance;

  public HighJump(final Player player, final HighJumpType highJumpType) {
    super(player);
    if (!bPlayer.canBend(this)) {
      return;
    }
    this.highJumpType = highJumpType;
    this.setFields();
    this.start();
  }

  private void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.playParticles = ConfigManager.getConfig().getBoolean("ExtraAbilities.xNuminousx.HighJump.Particles.Enabled");
    this.enableEvade = ConfigManager.getConfig().getBoolean("ExtraAbilities.xNuminousx.HighJump.Evade.Enabled");
    this.enableJump = ConfigManager.getConfig().getBoolean("ExtraAbilities.xNuminousx.HighJump.Jump.Enabled");
    this.enableDoubleJump = ConfigManager.getConfig().getBoolean("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Enabled");
    this.enableLunge = ConfigManager.getConfig().getBoolean("ExtraAbilities.xNuminousx.HighJump.Lunge.Enabled");
    this.jumpCooldown = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Jump.Cooldown", currentLevel);
    this.jumpHeight = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Jump.Height", currentLevel);
    this.doubleJumpCooldown = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Cooldown", currentLevel);
    this.doubleJumpHeight = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Height", currentLevel);
    this.lungeCooldown = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Lunge.Cooldown", currentLevel);
    this.lungeHeight = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Lunge.Height", currentLevel);
    this.lungeDistance = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Lunge.Distance", currentLevel);
    this.evadeCooldown = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Evade.Cooldown", currentLevel);
    this.evadeHeight = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Evade.Height", currentLevel);
    this.evadeDistance = TLBMethods.getLong("ExtraAbilities.xNuminousx.HighJump.Evade.Distance", currentLevel);
    this.location = this.player.getLocation().clone();
  }

  public void progress() {
    if (this.player.isDead() || !this.player.isOnline()) {
      this.remove();
      return;
    }
    switch (this.highJumpType) {
      case DOUBLEJUMP: {
        this.onDoubleJump();
      }
      case EVADE: {
        this.onEvade();
      }
      case JUMP: {
        this.onJump();
      }
      case LUNGE: {
        this.onLunge();
        break;
      }
    }
  }

  private void onDoubleJump() {
    if (this.enableDoubleJump && this.highJumpType == HighJumpType.DOUBLEJUMP) {

      fallDistance = this.player.getFallDistance();
      this.player.setFallDistance(0);
      final Vector vec = this.player.getVelocity();
      vec.setY((float) this.doubleJumpHeight);
      this.player.setVelocity(vec);
      this.poof();
      this.bPlayer.addCooldown(this, this.doubleJumpCooldown);

      this.player.setFallDistance(fallDistance);
      this.remove();
    }
  }

  private void onEvade() {
    if (this.enableEvade && this.highJumpType == HighJumpType.EVADE) {
      final Vector vec = this.player.getLocation().getDirection().normalize().multiply((float) (-this.evadeDistance));
      vec.setY((float) this.evadeHeight);
      this.player.setVelocity(vec);
      this.poof();
      this.bPlayer.addCooldown(this, this.evadeCooldown);
      this.remove();
    }
  }

  private void onJump() {
    if (this.enableJump && this.highJumpType == HighJumpType.JUMP) {

      fallDistance = this.player.getFallDistance();
      this.player.setFallDistance(0);
      final Vector vec = this.player.getVelocity();
      vec.setY((float) this.jumpHeight);
      this.player.setVelocity(vec);
      this.poof();
      this.bPlayer.addCooldown(this, this.jumpCooldown);

      this.player.setFallDistance(fallDistance);
      this.remove();
    }
  }

  private void onLunge() {
    if (this.enableLunge && this.highJumpType == HighJumpType.LUNGE) {
      fallDistance = this.player.getFallDistance();
      this.player.setFallDistance(0);

      final Vector vec = this.player.getLocation().getDirection().normalize().multiply((float) this.lungeDistance);
      vec.setY((float) this.lungeHeight);
      this.player.setVelocity(vec);
      this.poof();
      this.bPlayer.addCooldown(this, this.lungeCooldown);
      this.player.setFallDistance(fallDistance);
      this.remove();
    }
  }

  private void poof() {
    if (this.playParticles) {
      this.player.getLocation();
      ParticleEffect.CRIT.display(this.location, 20, 0.5, 1.0, 0.5, 0.5);
      ParticleEffect.CLOUD.display(this.location, 25, 0.5, 1.0, 0.5, 0.0020000000949949026);
    }
  }

  public boolean isSneakAbility() {
    return true;
  }

  public boolean isHarmlessAbility() {
    return true;
  }

  public long getCooldown() {
    switch (this.highJumpType) {
      case JUMP: {
        return this.jumpCooldown;
      }
      case EVADE: {
        return this.evadeCooldown;
      }
      case LUNGE: {
        return this.lungeCooldown;
      }
      case DOUBLEJUMP: {
        return this.doubleJumpCooldown;
      }
      default: {
        return 0L;
      }
    }
  }

  public String getName() {
    return "HighJump";
  }

  public String getDescription() {
    return ConfigManager.languageConfig.get().getString("ExtraAbilities.xNuminousx.HighJump.Description");
  }

  public String getInstructions() {
    return ConfigManager.languageConfig.get().getString("ExtraAbilities.xNuminousx.HighJump.Instructions");
  }

  public Location getLocation() {
    return this.location;
  }

  public String getVersion() {
    return "1.0.0";
  }

  public String getAuthor() {
    return "TLB (Original: Numin)";
  }

  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new HighJumpListener(), ProjectKorra.plugin);
    ConfigManager.languageConfig.get().addDefault("ExtraAbilities.xNuminousx.HighJump.Description", "Chiblockers are skilled acrobats and this HighJump Replacement satisfies those abilities! Now, you can lunge forward, lunge backwards, activate a double jump!");
    ConfigManager.languageConfig.get().addDefault("ExtraAbilities.xNuminousx.HighJump.Instructions", "Left-Click: Jump up. Tap-Shift: Lunge backwards. Spint+Click: Lunge Forwards. Left click in the air (level 7+): Double Jump");
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Particles.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Jump.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Lunge.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Evade.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Jump.Cooldown", 3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Jump.Height", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Cooldown", 2000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.DoubleJump.Height", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Lunge.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Lunge.Height", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Lunge.Distance", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Evade.Cooldown", 5000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Evade.Height", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.xNuminousx.HighJump.Evade.Distance", 2);
    ConfigManager.defaultConfig.save();
    ConfigManager.languageConfig.save();
    ProjectKorra.log.info("Successfully enabled " + this.getName() + " by " + this.getAuthor());
  }

  public void stop() {
    super.remove();
    ProjectKorra.log.info("Successfully disabled " + this.getName() + " by " + this.getAuthor());
  }

  public enum HighJumpType {
    EVADE,
    LUNGE,
    DOUBLEJUMP,
    JUMP
  }
}
