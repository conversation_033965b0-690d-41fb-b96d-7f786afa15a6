package com.thelastblockbender.highjump;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Material;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

public class HighJumpListener implements Listener {
  @EventHandler
  public void onSneak(final PlayerToggleSneakEvent event) {
    boolean isOnBlock = false;
    final Player player = event.getPlayer();
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
    final Material block = player.getLocation().getBlock().getRelative(BlockFace.DOWN).getType();
    if (block.isSolid()) {
      isOnBlock = true;
    }
    if (bPlayer.getBoundAbilityName().equalsIgnoreCase("HighJump")) {
      if (isOnBlock) {
        new HighJump(player, HighJump.HighJumpType.EVADE);
      }
    }
  }

  @EventHandler
  public void onSwing(final PlayerAnimationEvent event) {
    final Player player = event.getPlayer();
    final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
    if (bPlayer.getBoundAbilityName().equalsIgnoreCase("HighJump")) {
      final Material block = player.getLocation().getBlock().getRelative(BlockFace.DOWN).getType();

      int statLevel = StatisticsMethods.getId("AbilityLevel_HighJump");
      long currentLevel = TLBMethods.limitLevels(player, statLevel);

      if (block.isSolid() || currentLevel > 6) {
        if (player.isSprinting()) {
          new HighJump(player, HighJump.HighJumpType.LUNGE);
        } else {
          new HighJump(player, HighJump.HighJumpType.JUMP);
        }
      }
    }
  }
}
