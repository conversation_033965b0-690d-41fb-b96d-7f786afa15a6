package me.vahagn.nimbus;

import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerAnimationEvent;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;

public class NimbusListener implements Listener
{
    @EventHandler
    public void onSwing(final PlayerAnimationEvent event) {
        if (!event.getPlayer().isOnGround()) {
            final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
            if (bPlayer != null && bPlayer.canBend(CoreAbility.getAbility("Nimbus")) && event.getPlayer().isSprinting()) {
                new Nimbus(event.getPlayer());
            }
        }
    }

    @EventHandler
	public void onEntityDamageEvent(final EntityDamageEvent event) {
		final Entity entity = event.getEntity();

		if (entity instanceof Player) {
			final Player player = (Player) entity;
			final BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);
			if (bPlayer == null) {
				return;
			}
			if (CoreAbility.hasAbility(player, Nimbus.class)) {
				final Nimbus abil = CoreAbility.getAbility(player, Nimbus.class);
				abil.remove();
			}
		}
	}
}

