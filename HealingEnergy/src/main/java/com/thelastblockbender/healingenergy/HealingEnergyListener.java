package com.thelastblockbender.healingenergy;

import com.projectkorra.projectkorra.BendingPlayer;
import com.projectkorra.projectkorra.ability.CoreAbility;
import com.projectkorra.projectkorra.ability.util.ComboManager;
import com.projectkorra.projectkorra.airbending.Suffocate;
import com.projectkorra.projectkorra.util.ClickType;
import com.projectkorra.projectkorra.waterbending.blood.Bloodbending;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.player.PlayerAnimationEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.inventory.EquipmentSlot;

public class HealingEnergyListener implements Listener {
  @EventHandler
  public void onSwing(PlayerAnimationEvent event) {
    if (event.isCancelled()) {
      return;
    } else if (CoreAbility.hasAbility(event.getPlayer(), HealingEnergy.class)) {
      event.setCancelled(true);
      return;
    }
    new HealingEnergy(event.getPlayer());

  }

  long cooldown = CoreAbility.getConfig().getLong("ExtraAbilities.Water.HealingEnergy.Cooldown");

  @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
  public void onEntityDamageEvent(EntityDamageEvent event) {
    if (event.getEntity() instanceof Player) {
      HealingEnergy instance = CoreAbility.getAbility((Player) event.getEntity(), HealingEnergy.class);
      if (instance != null) {
        instance.onDamage();
      }
    }
  }

  @EventHandler
  public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
    if (event.isCancelled()) {
      return;
    }

    Player player = event.getPlayer();
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);

    if (bPlayer.canCurrentlyBendWithWeapons()) {
      ComboManager.addComboAbility(player, ClickType.RIGHT_CLICK_ENTITY);
    }

    if (Bloodbending.isBloodbent(player) || Suffocate.isBreathbent(player)) {
      event.setCancelled(true);
    }

    if (bPlayer.getBoundAbilityName().equalsIgnoreCase("HealingEnergy") && event.getHand().equals(EquipmentSlot.HAND)) {
      HealingEnergy instance = CoreAbility.getAbility(player, HealingEnergy.class);
      if (instance != null && instance.charged) {
        instance.click();
        event.setCancelled(true);
      }
    }
  }
}
