package com.thelastblockbender.healingenergy;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Particle.DustOptions;
import org.bukkit.attribute.Attribute;
import org.bukkit.attribute.AttributeInstance;
import org.bukkit.entity.Creature;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.HealingAbility;
import com.projectkorra.projectkorra.chiblocking.Smokescreen;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ColoredParticle;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class HealingEnergy extends HealingAbility implements AddonAbility {
  private long cooldown;
  private double range;
  private long chargeTime;
  private int power;
  private int duration;
  private int durationConcentration;
  private int powerMultiplier;

  private LivingEntity target;
  private Location location;
  public boolean charged = false;
  private int point;
  private long chargeDuration;

  public HealingEnergy(Player player) {
    super(player);

    if (!bPlayer.canBend(this)) {
      remove();
      return;
    }

    setFields();
    this.location = player.getLocation().clone().add(player.getLocation().getDirection()).add(0, 1.5, 0);

    modify();
    start();
  }

  public void setFields() {
    cooldown = getConfig().getLong("ExtraAbilities.Water.HealingEnergy.Cooldown");
    range = getConfig().getDouble("ExtraAbilities.Water.HealingEnergy.Range");
    chargeTime = getConfig().getLong("ExtraAbilities.Water.HealingEnergy.Chargetime");
    power = getConfig().getInt("ExtraAbilities.Water.HealingEnergy.Power");
    duration = getConfig().getInt("ExtraAbilities.Water.HealingEnergy.Duration");
    powerMultiplier = getConfig().getInt("ExtraAbilities.Water.HealingEnergy.PowerMultiplier");
  }

  private void modify() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());

    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.chargeDuration = (int) ((currentLevel * 600) + 4000);
    this.durationConcentration = (int) ((currentLevel * 300) + 500);
    this.chargeTime = (int) (5000 - (currentLevel * 300));

  }

  public void onDamage() {
    bPlayer.addCooldown(this);
    remove();
  }

  @Override
  public void progress() {
    if (!bPlayer.canBend(this)) {
      remove();
      return;
    }

    if (System.currentTimeMillis() >= getStartTime() + this.durationConcentration) {
      if (target instanceof Player) {
        if (target.isDead()) {
          target = null;
        }
        if (player.getLocation().distance(target.getLocation()) >= range) {
          target = null;
        }
      } else if (target instanceof Creature) {
        if (target.isDead()) {
          target = null;
        } else {
          if (player.getLocation().distance(target.getLocation()) >= range) {
            target = null;
          }
        }
      }
      if (System.currentTimeMillis() >= getStartTime() + chargeTime) {
        if (!charged) {
          this.charged = true;
        }
      }
      if (!charged) {
        prepare();
      }

      if (charged) {
        if (System.currentTimeMillis() < this.getStartTime() + this.chargeDuration + chargeTime) {
          if (target == null) {
            displayHealingEnergy(player);
            heal(player);
          } else {
            displayHealingEnergy(target);
            heal(target);
          }
        } else {
          this.charged = false;
          this.bPlayer.addCooldown(this, cooldown);
          remove();
        }
      }
    }
  }

  public void click() {
    Entity target = GeneralMethods.getTargetedEntity(player, range);
    if (target != null && !target.equals(this.target) && target instanceof LivingEntity) {
      if (target instanceof Player || target instanceof Creature) {
        this.target = (LivingEntity) target;
      }
    } else if (target != null && target.equals(this.target) && target instanceof LivingEntity) {
      this.target = null;
    }
  }

  public void healSelf() {
    this.target = player;
  }

  private void heal(LivingEntity target) {
    AttributeInstance temp = target.getAttribute(Attribute.MAX_HEALTH);
    if (temp == null) {
      return;
    }
    double maxHealth = temp.getValue();

    if (target.getHealth() < maxHealth) {
      for (PotionEffect effect : target.getActivePotionEffects()) {
        if (isNegativeEffect(effect.getType())) {
          if ((effect.getType() == PotionEffectType.BLINDNESS)
            && Smokescreen.getBlindedTimes().containsKey(target.getName())) {
            return;
          }
          target.removePotionEffect(effect.getType());
        }
      }
      if (target != super.player) {
        target.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, duration, power));
        super.player.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, duration, power));
      }
    }
  }

  private void prepare() {
    Location loc = GeneralMethods.getTargetedLocation(player, 3);
    DustOptions dustOptions = new DustOptions(Color.fromRGB(74, 228, 255), 1);
    ParticleEffect.REDSTONE.display(loc, 1, 0.01, 0.01, 0.01, 0.03, dustOptions);
  }

  private void displayHealingEnergy(LivingEntity target) {
    AttributeInstance temp = target.getAttribute(Attribute.MAX_HEALTH);
    if (temp == null) {
      return;
    }
    double maxHealth = temp.getValue();

    ColoredParticle particle = new ColoredParticle(Color.fromRGB(2, 169, 230), 1);
    if (target.getHealth() < maxHealth) {
      point++;
      if (point >= 32) {
        point = 0;
      }
      Location center = target.getLocation();
      for (int i = 0; i < 3; i++) {
        double radians = Math.toRadians(point + i * 90);
        Location spawnLoc = center.clone().add(Math.cos(radians), 1, Math.sin(radians));
        particle.display(spawnLoc, 1, 0, 0, 0);
      }
    } else {
      if (target instanceof Creature) {
        Location t = target.getEyeLocation().clone().add(0, 0.5, 0);
        if (player.isSneaking()) {
          particle.display(t, 1, 0, 0, 0);
        }
        particle.display(t, 1, 0, 0, 0);
        particle.display(t, 2, 0.0123, 0.0123, 0.0123);
      } else if (target instanceof Player) {
        Location p = GeneralMethods.getTargetedLocation(player, 3);
        if (player.isSneaking()) {
          particle.display(p, 2, 0, 0, 0);
        }
        particle.display(p, 2, 0.0123, 0.0123, 0.0123);
        particle.display(p, 2, 0.2, 0.2, 0.2);
        if (!target.equals(player)) {
          Location t = target.getEyeLocation().clone().add(0, 0.5, 0);
          if (player.isSneaking()) {
            //GeneralMethods.displayColoredParticle(t, hex2);
            particle.display(p, 1, 0, 0, 0);
          }
          particle.display(t, 1, 0, 0, 0);
          particle.display(t, 2, 0.0123, 0.0123, 0.0123);
        }
      }
    }
  }

  @Override
  public void remove() {
    super.remove();
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public boolean isHarmlessAbility() {
    return true;
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public String getName() {
    return "HealingEnergy";
  }

  @Override
  public String getDescription() {
    return "This ability allows water benders to concentrate the water around them with their own energy to promote healing energy in others.";
  }

  @Override
  public String getInstructions() {
    return "Left click to begin concentrating the water around you.\n"
      + "Once charged, right click a mob or player to envelop them in a combination of water and your own energy.\n";
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new HealingEnergyListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded!");
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.Chargetime", 3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.Range", 5);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.Duration", 100);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.Power", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.PowerMultiplier", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Water.HealingEnergy.Cooldown", 8000);

  }

  @Override
  public void stop() {
    super.remove();

  }

  @Override
  public String getAuthor() {
    return "TLB";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }
}
