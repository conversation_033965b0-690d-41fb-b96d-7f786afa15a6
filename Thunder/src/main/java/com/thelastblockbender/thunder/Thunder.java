package com.thelastblockbender.thunder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.LightningAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.MovementHandler;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import com.projectkorra.projectkorra.util.TempBlock;
import me.moros.hyperion.util.MaterialCheck;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class Thunder extends LightningAbility implements AddonAbility {

  private final Map<Integer, Location> branches = new HashMap<>();

  private long cooldown;
  private long time;
  private long chargeTime;
  private long regenTime;
  private double range;
  private double damage;
  private double branchSpace;
  private double stunChance;
  private long stunDuration;
  private boolean charged;
  private boolean launched;
  private boolean damageBlocks;
  private int spaces;
  private int currPoint;
  private static int explosionPower;
  private int fireTicks;

  private Vector direction;
  private Location origin;
  private Location location;
  private String hex;

  public Thunder(Player player) {
    super(player);

    if (!bPlayer.canBend(this) || hasAbility(player, Thunder.class)) {
      return;
    }

    setFields();
    start();
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    this.cooldown = TLBMethods.getLong("ExtraAbilities.Prride.Thunderclap.Cooldown", currentLevel);
    this.range = TLBMethods.getDouble("ExtraAbilities.Prride.Thunderclap.Range", currentLevel);
    this.damage = TLBMethods.getDouble("ExtraAbilities.Prride.Thunderclap.Damage", currentLevel);
    this.chargeTime = TLBMethods.getLong("ExtraAbilities.Prride.Thunderclap.ChargeTime", currentLevel);
    this.stunChance = TLBMethods.getDouble("ExtraAbilities.Prride.Thunderclap.StunChance", currentLevel);
    this.stunDuration = TLBMethods.getLong("ExtraAbilities.Prride.Thunderclap.StunDuration", currentLevel);
    this.fireTicks = TLBMethods.getInt("ExtraAbilities.Prride.Thunderclap.FireTicks", currentLevel);
    this.damageBlocks = ConfigManager.getConfig().getBoolean("ExtraAbilities.Prride.Thunderclap.DamageBlocks.Enabled");
    this.regenTime = TLBMethods.getLong("ExtraAbilities.Prride.Thunderclap.DamageBlocks.RegenTime", currentLevel);
    explosionPower = TLBMethods.getInt("ExtraAbilities.Prride.Thunderclap.DamageBlocks.ExplosionPower", currentLevel);

    time = System.currentTimeMillis();
    branchSpace = 0.2;
    hex = "33abcc";
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return location;
  }

  @Override
  public String getName() {
    return "Thunder";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return true;
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreCooldowns(this)) {
      remove();
      return;
    }
    if (launched) {
      if (origin.distanceSquared(location) > range * range) {
        remove();
        return;
      }
      advanceLocation();
    } else {
      if (player.isSneaking()) {
        displayParticleRing(60, 1.75F, 2);
      } else {
        if (!charged) {
          remove();
          return;
        } else {
          launch();
        }
      }
      if (charged) {
        GeneralMethods.displayColoredParticle(hex, player.getLocation(), 1, 0.5, 0.5, 0.5);
      } else {
        if (System.currentTimeMillis() > time + chargeTime) {
          charged = true;
        }
      }
    }
  }

  private void launch() {
    if (launched) {
      return;
    }
    location = player.getEyeLocation();
    origin = location.clone();
    direction = location.getDirection();
    branches.put(branches.size() + 1, location);
    bPlayer.addCooldown(this);
    launched = true;
  }

  private void advanceLocation() {
    spaces++;
    if (spaces % 3 == 0) {
      Location prevBranch = branches.get(1);
      branches.put(branches.size() + 1, prevBranch);
    }
    location.add(direction);
    List<Integer> cleanup = new ArrayList<>();
    for (int i : branches.keySet()) {
      Location origin = branches.get(i);
      if (origin != null) {
        Location l = origin.clone();
        if (!isTransparent(l.getBlock())) {
          cleanup.add(i);
          continue;
        }
        if (GeneralMethods.isSolid(l.getBlock()) || (!isTransparent(l.getBlock()) || isWater(l.getBlock())) && !GeneralMethods.isRegionProtectedFromBuild(player, l)) {
          createExplosion(l, explosionPower);
          return;
        }
        if (GeneralMethods.isSolid(l.getBlock())) {
          createExplosion(l, explosionPower);
          remove();
          return;
        }
        l.add(createBranch(), createBranch(), createBranch());
        branchSpace += 0.001;

        for (int j = 0; j < 5; j++) {
          GeneralMethods.displayColoredParticle(hex, l.clone(), 3, 0.5, 0.5, 0.5);
          if (j % 3 == 0) {
            if (checkEntities(l)) {
              remove();
              return;
            }
          }
          l = l.add(direction.clone().multiply(0.2));
        }
        branches.put(i, l);
      }
    }
    for (int i : cleanup) {
      Location origin = branches.get(i);
      if (origin != null) {
        Location l = origin.clone();
        createExplosion(l, explosionPower);
        branches.remove(i);
      }
    }
    cleanup.clear();
  }

  private boolean checkEntities(Location loc) {
    boolean hit = false;
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(loc, 2.0)) {
      if (entity.getEntityId() != player.getEntityId() && entity instanceof LivingEntity living) {
        onEntityHit(living, loc);
        hit = true;
      }
    }
    return hit;
  }

  public void explosion(Location loc) {
    ParticleEffect.FIREWORKS_SPARK.display(loc, 20, 1, 1, 1, 0.5f);
    GeneralMethods.displayColoredParticle(hex, loc, 1, 1, 1, 1);
    ParticleEffect.EXPLOSION_LARGE.display(loc, 5, 1, 1, 1, 0.5f);
    loc.getWorld().playSound(loc, Sound.ENTITY_GENERIC_EXPLODE, 1f, 1f);
  }

  private void createExplosion(Location loc, int size) {
    explosion(loc);
    if (damageBlocks) {
      for (Location l : GeneralMethods.getCircle(loc, size, 1, false, true, 0)) {
        if (GeneralMethods.isRegionProtectedFromBuild(player, l)) {
          continue;
        }
        if (l.getBlock().getType().isAir() || MaterialCheck.isUnbreakable(l.getBlock()) || l.getBlock().isLiquid()) {
          continue;
        }
        long delay = regenTime + ThreadLocalRandom.current().nextInt(1000);
        new TempBlock(l.getBlock(), Material.AIR.createBlockData(), delay);
      }
    }
  }

  private double createBranch() {
    return ThreadLocalRandom.current().nextInt(-1, 2) * branchSpace;
  }

  public void onEntityHit(LivingEntity entity, Location loc) {
    Vector knockbackVector = entity.getLocation().toVector().subtract(loc.toVector()).normalize().multiply(0.8);
    entity.setVelocity(knockbackVector);
    DamageHandler.damageEntity(entity, damage, this);
    entity.setFireTicks(fireTicks);
    GeneralMethods.displayColoredParticle(hex, entity.getLocation(), 14, 0.15, 0.15, 0.15);
    if (ThreadLocalRandom.current().nextDouble() <= stunChance) {
      entity.getWorld().playSound(entity.getLocation(), Sound.ENTITY_LIGHTNING_BOLT_IMPACT, 1, 0.01F);
      new MovementHandler(entity, this)
        .stopWithDuration(stunDuration / 1000 * 20, ChatColor.RED + "* Electrocuted *");
    }
  }

  public void displayParticleRing(int points, float size, int speed) {
    for (int i = 0; i < speed; ++i) {
      currPoint += 360 / points;
      if (currPoint > 360) {
        currPoint = 0;
      }
      double angle = Math.toRadians(currPoint);
      double x = size * Math.cos(angle);
      double z = size * Math.sin(angle);
      Location loc = player.getLocation().add(x, 1.0D, z);
      GeneralMethods.displayColoredParticle(hex, loc, 1, 0F, 0F, 0F);
      if (charged) {
        ParticleEffect.CRIT.display(loc, 2, 0f, 0f, 0f, 0);
      }
    }
  }

  @Override
  public String getAuthor() {
    return "TLB (Original: Prride)";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public String getDescription() {
    return getName() + " Version " + getVersion() + " is created by " + getAuthor() + "\nLightning benders can concentrate their lightning into stream that creates a wave of thunder, causing damage to anyone who is hit.";
  }

  @Override
  public String getInstructions() {
    return "Hold sneak until you see particles beneath your feet. When you see those particles, release to shoot a blast of thunder that you can control.";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new ThunderListener(), ProjectKorra.plugin);
    ProjectKorra.log.info(getName() + " " + getVersion() + " by " + getAuthor() + " loaded! ");

    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.Cooldown", 10000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.ChargeTime", 3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.StunDuration", 200);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.StunChance", 100);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.Range", 35);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.Damage", 3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.FireTicks", 200);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.DamageBlocks.Enabled", true);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.DamageBlocks.RegenTime", 6000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Prride.Thunderclap.DamageBlocks.ExplosionPower", 3);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
  }
}
