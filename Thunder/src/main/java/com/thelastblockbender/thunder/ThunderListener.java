package com.thelastblockbender.thunder;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerToggleSneakEvent;

import com.projectkorra.projectkorra.BendingPlayer;

public class ThunderListener implements Listener {

	@EventHandler
	public void onSneak(PlayerToggleSneakEvent event) {
		if (event.isCancelled()) return;

		if (!event.isSneaking()) return;

		BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(event.getPlayer());
		if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("Thunder")) {
			new Thunder(event.getPlayer());
		}
	}
}
