package com.thelastblockbender.bola;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.projectkorra.projectkorra.GeneralMethods;
import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.ChiAbility;
import com.projectkorra.projectkorra.chiblocking.passive.ChiPassive;
import com.projectkorra.projectkorra.command.Commands;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ColoredParticle;
import com.projectkorra.projectkorra.util.DamageHandler;
import com.projectkorra.projectkorra.util.StatisticsMethods;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.Vector;

public class Bola extends ChiAbility implements AddonAbility {
  private static final String BOLA_METADATA_KEY = "projectkorra-bola";

  private static final Map<LivingEntity, BukkitTask> TRACKED_ENTITIES = new HashMap<>();

  private final BolaData[] data = new BolaData[3];
  private final List<ThrownBola> activeBolas = new ArrayList<>();

  private int thrownBolas = 0;
  private int maxBolas;
  private double speed;
  private double range;
  private double collisionRadius;
  private long cooldown;
  private long duration;
  private long delay;
  private long lastLaunchTime;

  public Bola(Player player) {
    super(player);

    if (!bPlayer.canBend(this)) {
      return;
    }

    Bola instance = getAbility(player, Bola.class);
    if (instance != null) {
      instance.launchBola();
      return;
    }

    lastLaunchTime = 0;
    setFields();
    launchBola();
    start();
  }

  private void launchBola() {
    if (thrownBolas >= maxBolas) {
      return;
    }
    long time = System.currentTimeMillis();
    if (delay != 0 && time < lastLaunchTime + delay) {
      return;
    }
    lastLaunchTime = time;
    if (++thrownBolas >= maxBolas) {
      bPlayer.addCooldown(this);
    }
    activeBolas.add(new ThrownBola(player, range, speed));
    player.getWorld().playSound(player.getEyeLocation(), Sound.ENTITY_ARROW_SHOOT, 1, 0);
  }

  public void setFields() {
    int statLevel = StatisticsMethods.getId("AbilityLevel_" + getName());
    long currentLevel = TLBMethods.limitLevels(player, statLevel);

    speed = TLBMethods.getDouble("ExtraAbilities.Hiro3.Chi.Bola.Speed", currentLevel);
    range = TLBMethods.getDouble("ExtraAbilities.Hiro3.Chi.Bola.Range", currentLevel);
    collisionRadius = TLBMethods.getDouble("ExtraAbilities.Hiro3.Chi.Bola.CollisionRadius", currentLevel);
    cooldown = TLBMethods.getLong("ExtraAbilities.Hiro3.Chi.Bola.Cooldown", currentLevel);
    duration = TLBMethods.getLong("ExtraAbilities.Hiro3.Chi.Bola.MaxShotTime", currentLevel);
    delay = TLBMethods.getLong("ExtraAbilities.Hiro3.Chi.Bola.TimeBetweenShots", currentLevel);
    duration += 2 * delay;

    String[] prefixes = {"First", "Second", "Third"};
    for (int i = 0; i < 3; i++) {
      String s = prefixes[i];
      double damage = TLBMethods.getDouble("ExtraAbilities.Hiro3.Chi.Bola." + s + "Damage", currentLevel);
      int duration = TLBMethods.getInt("ExtraAbilities.Hiro3.Chi.Bola." + s + "Duration", currentLevel);
      int amplifier = TLBMethods.getInt("ExtraAbilities.Hiro3.Chi.Bola." + s + "Slowness", currentLevel) - 1;
      data[i] = new BolaData(damage, duration, amplifier);
    }

    if (currentLevel <= 3) {
      maxBolas = 1;
    } else if (currentLevel <= 6) {
      maxBolas = 2;
    } else {
      maxBolas = 3;
    }
  }

  private LivingEntity checkEntities(Location center) {
    for (Entity entity : GeneralMethods.getEntitiesAroundPoint(center, collisionRadius)) {
      if (entity instanceof LivingEntity && entity.getEntityId() != player.getEntityId() && !(entity instanceof ArmorStand)) {
        if (entity instanceof Player && Commands.invincible.contains((entity).getName())) {
          continue;
        }
        return (LivingEntity) entity;
      }
    }
    return null;
  }

  private void onEntityHit(LivingEntity entity) {
    if (!entity.isValid()) {
      return;
    }
    int count = 1;
    if (entity.hasMetadata(BOLA_METADATA_KEY)) {
      BolaMetadata meta = (BolaMetadata) entity.getMetadata(BOLA_METADATA_KEY).get(0).value();
      if (meta != null && System.currentTimeMillis() <= meta.expireTime) {
        count = Math.min(3, meta.count + 1);
      }
    }
    BolaData bd = getBolaType(count);
    entity.setMetadata(BOLA_METADATA_KEY, new FixedMetadataValue(ProjectKorra.plugin, new BolaMetadata(count, bd.effectDuration)));
    if (bd.damage > 0) {
      DamageHandler.damageEntity(entity, player, bd.damage, this);
    }
    if (entity.isValid()) {
      attemptTrackTarget(entity);
      player.getWorld().playSound(entity.getEyeLocation(), Sound.ENTITY_PLAYER_ATTACK_CRIT, 1, 0);
      int ticks = bd.effectDuration / 50;
      entity.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, ticks, bd.effectAmplifier, true, false));
      if (count == 3 && entity instanceof Player other) {
        ChiPassive.blockChi(player, other);
      }
    }
  }

  private void untrack(LivingEntity entity) {
    TRACKED_ENTITIES.remove(entity);
    entity.removeMetadata(BOLA_METADATA_KEY, ProjectKorra.plugin);
  }

  public void attemptTrackTarget(final LivingEntity target) {
    if (TRACKED_ENTITIES.containsKey(target)) {
      return;
    }
    BukkitRunnable runnable = new BukkitRunnable() {
      @Override
      public void run() {
        if (!target.isValid() || (target instanceof Player && !((Player) target).isOnline())) {
          untrack(target);
          cancel();
          return;
        }
        if (target.hasMetadata(BOLA_METADATA_KEY)) {
          BolaMetadata meta = (BolaMetadata) target.getMetadata(BOLA_METADATA_KEY).get(0).value();
          if (meta != null && System.currentTimeMillis() <= meta.expireTime) {
            renderAroundTarget(target, getYOffset(meta.count));
            return;
          }
        }
        untrack(target);
        cancel();
      }
    };
    BukkitTask scheduledTask = runnable.runTaskTimer(ProjectKorra.plugin, 1, 4);
    TRACKED_ENTITIES.put(target, scheduledTask);
  }

  private static class BolaMetadata {
    public final long expireTime;
    public final int count;

    private BolaMetadata(int count, long duration) {
      this.count = count;
      expireTime = System.currentTimeMillis() + duration;
    }
  }

  private static class ThrownBola {
    private final Location origin;
    private final Vector direction;
    private final double range;

    private Location location;

    private double phase;

    private ThrownBola(Player player, double range, double speed) {
      this.origin = player.getEyeLocation();
      this.location = origin.clone();
      this.direction = player.getEyeLocation().getDirection().multiply(speed);
      this.range = range;
    }

    private boolean progress() {
      location = location.add(direction);
      if (location.distanceSquared(origin) > range * range) {
        return false;
      }
      return render();
    }

    private Location getLocation() {
      return location;
    }

    private boolean render() {
      if (phase < 120) {
        phase += 20;
      } else {
        phase = 0;
      }

      if (GeneralMethods.isSolid(location.getBlock())) {
        return false;
      }

      getColoredParticle("4E5C5C").display(location, 1, 0, 0, 0);
      for (int i = 1; i <= 6; i++) {
        double pos = i * 0.2;
        for (int j = 0; j < 360; j += 120) {
          double radians = Math.toRadians(phase + j);
          final Vector temp = new Vector(pos * Math.cos(radians), i * 0.03, pos * Math.sin(radians));
          Location spawnLoc = location.clone().add(temp);
          if (pos > 0.5 && GeneralMethods.isSolid(spawnLoc.getBlock())) {
            return false;
          }
          getColoredParticle(i == 6 ? "81C6C8" : "4E5C5C").display(spawnLoc, 1, 0, 0, 0);
        }
      }
      return true;
    }
  }

  @Override
  public void progress() {
    if (!bPlayer.canBendIgnoreBindsCooldowns(this)) {
      bPlayer.addCooldown(this);
      remove();
      return;
    }

    if (activeBolas.isEmpty() && thrownBolas >= maxBolas) {
      // Cooldown handled in launchBola
      remove();
      return;
    }

    if (duration != 0 && System.currentTimeMillis() >= getStartTime() + duration) {
      bPlayer.addCooldown(this);
      remove();
      return;
    }

    Iterator<ThrownBola> it = activeBolas.iterator();
    while (it.hasNext()) {
      ThrownBola bola = it.next();
      if (!bola.progress()) {
        it.remove();
      } else {
        LivingEntity caughtTarget = checkEntities(bola.getLocation());
        if (caughtTarget != null) {
          onEntityHit(caughtTarget);
          it.remove();
        }
      }
    }
  }

  @Override
  public long getCooldown() {
    return cooldown;
  }

  @Override
  public Location getLocation() {
    return player.getLocation();
  }

  @Override
  public List<Location> getLocations() {
    return activeBolas.stream().map(ThrownBola::getLocation).collect(Collectors.toList());
  }

  @Override
  public String getName() {
    return "Bola";
  }

  @Override
  public boolean isHarmlessAbility() {
    return false;
  }

  @Override
  public boolean isSneakAbility() {
    return false;
  }

  @Override
  public String getDescription() {
    return "Throw your bola at your targets to slow and catch them!";
  }

  @Override
  public String getInstructions() {
    return "Left Click";
  }

  @Override
  public String getAuthor() {
    return "TLB (Original: Hiro3)";
  }

  @Override
  public String getVersion() {
    return "1.0.0";
  }

  @Override
  public void load() {
    ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new BolaListener(), ProjectKorra.plugin);
    ProjectKorra.log.info("Succesfully enabled " + getName() + " by " + getAuthor());

    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.Cooldown", 4000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.MaxShotTime", 4000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.TimeBetweenShots", 500);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.Speed", 1.25);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.Range", 20);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.CollisionRadius", 0.9);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.FirstSlowness", 1);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.FirstDuration", 1000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.FirstDamage", 0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.SecondSlowness", 2);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.SecondDuration", 2000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.SecondDamage", 0);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.ThirdSlowness", 3);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.ThirdDuration", 3000);
    ConfigManager.getConfig().addDefault("ExtraAbilities.Hiro3.Chi.Bola.ThirdDamage", 0);
    ConfigManager.defaultConfig.save();
  }

  @Override
  public void stop() {
    ProjectKorra.log.info("Successfully disabled " + getName() + " by " + getAuthor());
    super.remove();
  }

  private static double getYOffset(int count) {
    switch (count) {
      case 3:
        return 1.3;
      case 2:
        return 0.9;
      default:
      case 1:
        return 0.3;
    }
  }

  private static void renderAroundTarget(LivingEntity target, double yOffset) {
    for (int i = 0; i < 360; i += 10) {
      double radians = Math.toRadians(i);
      Location spawnLoc = target.getLocation().add(0.6 * Math.cos(radians), yOffset, 0.6 * Math.sin(radians));
      getColoredParticle("4E5C5C").display(spawnLoc, 1, 0, 0, 0);
    }
  }

  private static ColoredParticle getColoredParticle(String hexVal) {
    int r = 0;
    int g = 0;
    int b = 0;
    if (hexVal.length() <= 6) {
      r = Integer.valueOf(hexVal.substring(0, 2), 16);
      g = Integer.valueOf(hexVal.substring(2, 4), 16);
      b = Integer.valueOf(hexVal.substring(4, 6), 16);
    }
    return new ColoredParticle(Color.fromRGB(r, g, b), 0.8F);
  }

  private BolaData getBolaType(int count) {
    int index = Math.max(0, Math.min(data.length, count - 1));
    return data[index];
  }

  private static class BolaData {
    public final double damage;
    public final int effectDuration;
    public final int effectAmplifier;

    private BolaData(double damage, int effectDuration, int effectAmplifier) {
      this.damage = damage;
      this.effectDuration = effectDuration;
      this.effectAmplifier = effectAmplifier;
    }
  }
}
