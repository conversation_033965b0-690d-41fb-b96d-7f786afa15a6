package com.thelastblockbender.bola;

import com.projectkorra.projectkorra.BendingPlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerAnimationEvent;

public class BolaListener implements Listener {

  @EventHandler(ignoreCancelled = true)
  public void onClick(PlayerAnimationEvent event) {
    Player player = event.getPlayer();
    BendingPlayer bPlayer = BendingPlayer.getBendingPlayer(player);

    if (bPlayer != null && bPlayer.getBoundAbilityName().equalsIgnoreCase("Bola")) {
      new <PERSON><PERSON>(player);
    }
  }
}
